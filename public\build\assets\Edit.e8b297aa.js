import{j as p,T as $,a as m,b as s,u as a,w as x,F as T,d as u,Z as B,e as t,t as n,x as z,f as c,r as I,y as R}from"./app.0e820f21.js";import{_ as D,a as L}from"./AdminLayout.687face1.js";import{_,a as y}from"./TextInput.a134c4d6.js";import{_ as r}from"./InputLabel.c491b164.js";import{P as O}from"./PrimaryButton.259b896f.js";import{_ as H}from"./TextArea.3742605b.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"animate-top bg-white p-4 shadow sm:p-6 rounded-lg border"},M={class:"text-xl sm:text-2xl font-semibold leading-7 text-gray-900"},Z={class:"border-b border-gray-900/10 pb-12"},G={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},J={class:"sm:col-span-12"},K={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},W={class:"text-sm text-gray-700"},X={class:"text-sm text-gray-700"},tt={class:"text-sm text-gray-700"},et={class:"text-sm text-gray-700"},st={class:"text-sm text-gray-700"},lt={class:"text-sm text-gray-700"},at={class:"text-sm text-gray-700"},dt={class:"text-sm text-gray-700"},ot={key:0},it={class:"text-sm text-gray-700"},nt={class:"sm:col-span-3"},rt={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},mt={class:"sm:col-span-3"},ut={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},yt={class:"sm:col-span-3"},_t={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ct={class:"sm:col-span-3"},pt={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},gt={class:"sm:col-span-12"},vt={key:0,class:"sm:col-span-12"},xt={class:"mt-2 space-y-2"},ft={class:"flex items-center space-x-3"},qt={class:"text-sm text-gray-700"},bt={class:"flex items-center space-x-2"},Vt=["href"],wt=["onClick"],Qt={class:"flex mt-6 items-center justify-between"},kt={class:"ml-auto flex items-center justify-end gap-x-6"},Yt={__name:"Edit",props:{data:{type:Object,required:!0},counties:{type:Array,required:!0},filepath:{type:Object,default:null},permissions:{type:Object,default:()=>({})}},setup(i){var f;const o=i,g=p(((f=o.permissions)==null?void 0:f.canEditPrice)||!1),l=$({id:o.data.id,lead_id:o.data.lead_id||null,qty_1:o.data.lead.qty_1||"",qty_2:o.data.lead.qty_2||"",qty_3:o.data.lead.qty_3||"",qty_4:o.data.lead.qty_4||"",price_qty_1:o.data.price_qty_1||"",price_qty_2:o.data.price_qty_2||"",price_qty_3:o.data.price_qty_3||"",price_qty_4:o.data.price_qty_4||"",notes:o.data.notes||"",valid_until:o.data.valid_until||"",documents:[]}),E=p(!!o.data.lead.qty_1),P=p(!!o.data.lead.qty_2),U=p(!!o.data.lead.qty_3),Y=p(!!o.data.lead.qty_4),j=()=>{l.post(route("quotations.store"),{preserveScroll:!0})},S=v=>{confirm("Are you sure you want to remove this document?")&&(window.location.href=route("removequotationdocument",v))};return(v,e)=>(u(),m(T,null,[s(a(B),{title:"Quotations"}),s(D,null,{default:x(()=>{var q,b,V,w,Q,k,A,N,h,C;return[t("div",F,[t("h2",M," Edit Quotation - "+n(i.data.quotation_number),1),t("form",{onSubmit:z(j,["prevent"])},[t("div",Z,[t("div",G,[t("div",J,[e[18]||(e[18]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information",-1)),t("div",K,[t("div",null,[e[9]||(e[9]=t("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1)),t("p",W,n(((q=i.data.lead)==null?void 0:q.client_name)||"N/A"),1)]),t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-semibold text-gray-900"},"County",-1)),t("p",X,n(((V=(b=i.data.lead)==null?void 0:b.county)==null?void 0:V.name)||"N/A"),1)]),t("div",null,[e[11]||(e[11]=t("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1)),t("p",tt,n(((w=i.data.lead)==null?void 0:w.dimensions)||"N/A"),1)]),t("div",null,[e[12]||(e[12]=t("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1)),t("p",et,n(((Q=i.data.lead)==null?void 0:Q.open_size)||"N/A"),1)]),t("div",null,[e[13]||(e[13]=t("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1)),t("p",st,n(((k=i.data.lead)==null?void 0:k.box_style)||"N/A"),1)]),t("div",null,[e[14]||(e[14]=t("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1)),t("p",lt,n(((A=i.data.lead)==null?void 0:A.stock)||"N/A"),1)]),t("div",null,[e[15]||(e[15]=t("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1)),t("p",at,n(((N=i.data.lead)==null?void 0:N.lamination)||"N/A"),1)]),t("div",null,[e[16]||(e[16]=t("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1)),t("p",dt,n(((h=i.data.lead)==null?void 0:h.printing)||"N/A"),1)]),(C=i.data.lead)!=null&&C.add_ons?(u(),m("div",ot,[e[17]||(e[17]=t("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1)),t("p",it,n(i.data.lead.add_ons),1)])):c("",!0)])]),e[20]||(e[20]=t("div",{class:"sm:col-span-12 mt-6"},[t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quantities and Pricing")],-1)),t("div",nt,[E.value?(u(),m("div",rt,[t("div",null,[s(r,{for:"qty_1",value:"QTY 1 *"}),s(_,{id:"qty_1",type:"number",modelValue:a(l).qty_1,"onUpdate:modelValue":e[0]||(e[0]=d=>a(l).qty_1=d),min:"1",required:"",disabled:!0},null,8,["modelValue"]),s(y,{message:a(l).errors.qty_1},null,8,["message"])]),t("div",null,[s(r,{for:"price_qty_1",value:"PRICE QTY 1 *"}),s(_,{id:"price_qty_1",type:"number",step:"0.01",modelValue:a(l).price_qty_1,"onUpdate:modelValue":e[1]||(e[1]=d=>a(l).price_qty_1=d),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),s(y,{message:a(l).errors.price_qty_1},null,8,["message"])])])):c("",!0)]),t("div",mt,[P.value?(u(),m("div",ut,[t("div",null,[s(r,{for:"qty_2",value:"QTY 2"}),s(_,{id:"qty_2",type:"number",modelValue:a(l).qty_2,"onUpdate:modelValue":e[2]||(e[2]=d=>a(l).qty_2=d),min:"1",disabled:!0},null,8,["modelValue"]),s(y,{message:a(l).errors.qty_2},null,8,["message"])]),t("div",null,[s(r,{for:"price_qty_2",value:"PRICE QTY 2"}),s(_,{id:"price_qty_2",type:"number",step:"0.01",modelValue:a(l).price_qty_2,"onUpdate:modelValue":e[3]||(e[3]=d=>a(l).price_qty_2=d),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),s(y,{message:a(l).errors.price_qty_2},null,8,["message"])])])):c("",!0)]),t("div",yt,[U.value?(u(),m("div",_t,[t("div",null,[s(r,{for:"qty_3",value:"QTY 3"}),s(_,{id:"qty_3",type:"number",modelValue:a(l).qty_3,"onUpdate:modelValue":e[4]||(e[4]=d=>a(l).qty_3=d),min:"1",disabled:!0},null,8,["modelValue"]),s(y,{message:a(l).errors.qty_3},null,8,["message"])]),t("div",null,[s(r,{for:"price_qty_3",value:"PRICE QTY 3"}),s(_,{id:"price_qty_3",type:"number",step:"0.01",modelValue:a(l).price_qty_3,"onUpdate:modelValue":e[5]||(e[5]=d=>a(l).price_qty_3=d),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),s(y,{message:a(l).errors.price_qty_3},null,8,["message"])])])):c("",!0)]),t("div",ct,[Y.value?(u(),m("div",pt,[t("div",null,[s(r,{for:"qty_4",value:"QTY 4"}),s(_,{id:"qty_4",type:"number",modelValue:a(l).qty_4,"onUpdate:modelValue":e[6]||(e[6]=d=>a(l).qty_4=d),min:"1",disabled:!0},null,8,["modelValue"]),s(y,{message:a(l).errors.qty_4},null,8,["message"])]),t("div",null,[s(r,{for:"price_qty_4",value:"PRICE QTY 4"}),s(_,{id:"price_qty_4",type:"number",step:"0.01",modelValue:a(l).price_qty_4,"onUpdate:modelValue":e[7]||(e[7]=d=>a(l).price_qty_4=d),min:"0",disabled:!g.value},null,8,["modelValue","disabled"]),s(y,{message:a(l).errors.price_qty_4},null,8,["message"])])])):c("",!0)]),t("div",gt,[s(r,{for:"notes",value:"Notes"}),s(H,{id:"notes",modelValue:a(l).notes,"onUpdate:modelValue":e[8]||(e[8]=d=>a(l).notes=d),rows:"4"},null,8,["modelValue"]),s(y,{message:a(l).errors.notes},null,8,["message"])]),i.data.documents&&i.data.documents.length>0?(u(),m("div",vt,[s(r,{value:"Existing Documents"}),t("div",xt,[(u(!0),m(T,null,I(i.data.documents,d=>(u(),m("div",{key:d.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",ft,[e[19]||(e[19]=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),t("span",qt,n(d.orignal_name),1)]),t("div",bt,[t("a",{href:i.filepath.view+d.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,Vt),t("button",{type:"button",onClick:At=>S(d.id),class:"text-red-600 hover:text-red-800 text-sm"},"Remove",8,wt)])]))),128))])])):c("",!0)])]),t("div",Qt,[t("div",kt,[s(L,{href:v.route("quotations.index")},{svg:x(()=>e[21]||(e[21]=[t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),s(O,{disabled:a(l).processing},{default:x(()=>e[22]||(e[22]=[R(" Save ")])),_:1,__:[22]},8,["disabled"])])])],32)])]}),_:1})],64))}};export{Yt as default};
