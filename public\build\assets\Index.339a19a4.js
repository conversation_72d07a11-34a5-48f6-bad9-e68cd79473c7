import{_ as d}from"./AdminLayout.687face1.js";import{d as a,p as c,w as i,e,k as m,u as n,q as f,T as p,j as h,a as l,b as o,F as g,Z as u,f as _}from"./app.0e820f21.js";import"./plugin-vue_export-helper.21dcd24c.js";const x={class:"text-lg font-semibold leading-7 text-gray-900"},w={__name:"CustomButton",props:{href:{type:String,required:!0},active:{type:Boolean}},setup(t){return(r,s)=>(a(),c(n(f),{href:t.href,class:"flex justify-between items-center border border-gray-300 px-4 py-6 bg-white rounded-lg shadow-sm hover:shadow hover:border-gray-300"},{default:i(()=>[e("h3",x,[m(r.$slots,"default")])]),_:3},8,["href"]))}},v={class:"animate-top"},y={class:"border-gray-900 mt-10",style:{height:"500px"}},b={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},k={key:0,class:"sm:col-span-2"},$={__name:"Index",props:["permissions"],setup(t){return p({}),h(""),(r,s)=>(a(),l(g,null,[o(n(u),{title:"Settings"}),o(d,null,{default:i(()=>[e("div",v,[s[1]||(s[1]=e("div",{class:"sm:flex sm:items-center"},[e("div",{class:"sm:flex-auto"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Settings")]),e("div",{class:"flex space-x-6 mt-4 sm:mt-0 w-64"})],-1)),e("div",y,[e("div",b,[t.permissions.canPermissionsAdd?(a(),l("div",k,[o(w,{href:r.route("roles.index")},{default:i(()=>s[0]||(s[0]=[e("svg",{class:"w-12 h-12 fill-current text-indigo-600 inline-block",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M12 2l7 4v6c0 5-3 9-7 10-4-1-7-5-7-10V6l7-4z"}),e("circle",{cx:"12",cy:"10",r:"3",fill:"white"}),e("path",{d:"M9 16c1-2 5-2 6 0",stroke:"white","stroke-width":"2",fill:"none"})],-1),e("span",{class:"font-semibold text-lg ml-4"},"Roles & Permissions",-1)])),_:1,__:[0]},8,["href"])])):_("",!0)])])])]),_:1})],64))}};export{$ as default};
