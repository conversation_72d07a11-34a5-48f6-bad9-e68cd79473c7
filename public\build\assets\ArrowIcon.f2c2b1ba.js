import{d as o,a as t,e as s,f as r}from"./app.0e820f21.js";const l={key:0},i={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},a={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 inline-block ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"#0000FF"},k={__name:"ArrowIcon",props:{isSorted:{type:Boolean,default:!1},direction:{type:String,default:"asc"}},setup(n){return(d,e)=>n.isSorted?(o(),t("span",l,[n.direction==="asc"?(o(),t("svg",i,e[0]||(e[0]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"},null,-1)]))):(o(),t("svg",a,e[1]||(e[1]=[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)])))])):r("",!0)}};export{k as _};
