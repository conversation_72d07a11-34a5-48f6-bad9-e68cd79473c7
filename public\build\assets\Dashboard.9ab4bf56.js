import{a as g,b as r,u as n,w as i,F as m,d as c,Z as j,e,q as a,y as x,t as l,r as v,f as _,g as u}from"./app.0e820f21.js";import{_ as T}from"./AdminLayout.687face1.js";import"./plugin-vue_export-helper.21dcd24c.js";const C={class:"animate-top space-y-6"},V={class:"bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},D={class:"flex justify-between items-center"},M={class:"flex space-x-2"},B={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4"},S={class:"bg-white border rounded-lg p-6 shadow-sm"},A={class:"flex items-center"},N={class:"ml-4"},z={class:"text-2xl font-bold text-gray-900"},F={class:"bg-white border rounded-lg p-6 shadow-sm"},O={class:"flex items-center"},$={class:"ml-4"},L={class:"text-2xl font-bold text-yellow-600"},U={class:"bg-white border rounded-lg p-6 shadow-sm"},q={class:"flex items-center"},H={class:"ml-4"},P={class:"text-2xl font-bold text-red-600"},R={class:"bg-white border rounded-lg p-6 shadow-sm"},E={class:"flex items-center"},Q={class:"ml-4"},W={class:"text-2xl font-bold text-green-600"},Y={class:"bg-white border rounded-lg p-6 shadow-sm"},Z={class:"flex items-center"},G={class:"ml-4"},I={class:"text-2xl font-bold text-purple-600"},J={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},K={class:"bg-white border rounded-lg p-6 shadow-sm"},X={class:"flex justify-between items-center mb-4"},ee={class:"space-y-3"},te={class:"flex-1"},se={class:"flex items-center space-x-2"},oe={class:"text-sm font-medium text-gray-900 mt-1"},le={class:"text-xs text-gray-700"},re={class:"text-right"},ne={class:"text-xs text-gray-700"},ie={key:0,class:"text-center py-4 text-gray-700"},de={class:"bg-white border rounded-lg p-6 shadow-sm"},ae={class:"flex justify-between items-center mb-4"},ge={class:"space-y-3"},ce={class:"flex-1"},xe={class:"flex items-center space-x-2"},ue={class:"text-sm font-medium text-gray-900 mt-1"},me={class:"text-xs text-gray-700"},fe={class:"text-right"},pe={key:0,class:"text-center py-4 text-gray-700"},he={class:"bg-white border rounded-lg p-6 shadow-sm"},be={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},we={__name:"Dashboard",props:{stats:Object,recentTasks:Array,upcomingTasks:Array},setup(d){const f=o=>{const t=new Date(o),s=String(t.getDate()).padStart(2,"0"),w=String(t.getMonth()+1).padStart(2,"0"),k=t.getFullYear();return`${s}/${w}/${k}`},p=o=>o.replace("_"," ").replace(/\b\w/g,t=>t.toUpperCase()),h=o=>new Date(o)<new Date,b=o=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[o]||"bg-gray-100 text-gray-800",y=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800";return(o,t)=>(c(),g(m,null,[r(n(j),{title:"Tasks Dashboard"}),r(T,null,{default:i(()=>[e("div",C,[e("div",V,[e("div",D,[t[1]||(t[1]=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Tasks Dashboard"),e("p",{class:"text-sm text-gray-600 mt-1"},"Overview of your tasks and productivity")],-1)),e("div",M,[r(n(a),{href:o.route("tasks.index"),class:"px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:i(()=>t[0]||(t[0]=[x(" \u2190 Back to Tasks ")])),_:1,__:[0]},8,["href"])])])]),e("div",B,[e("div",S,[e("div",A,[t[3]||(t[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])])],-1)),e("div",N,[t[2]||(t[2]=e("p",{class:"text-sm font-medium text-gray-600"},"Total Tasks",-1)),e("p",z,l(d.stats.total_tasks),1)])])]),e("div",F,[e("div",O,[t[5]||(t[5]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",$,[t[4]||(t[4]=e("p",{class:"text-sm font-medium text-gray-600"},"Pending",-1)),e("p",L,l(d.stats.pending_tasks),1)])])]),e("div",U,[e("div",q,[t[7]||(t[7]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",H,[t[6]||(t[6]=e("p",{class:"text-sm font-medium text-gray-600"},"Overdue",-1)),e("p",P,l(d.stats.overdue_tasks),1)])])]),e("div",R,[e("div",E,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])])],-1)),e("div",Q,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600"},"Due Today",-1)),e("p",W,l(d.stats.due_today),1)])])]),e("div",Y,[e("div",Z,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",G,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600"},"This Week",-1)),e("p",I,l(d.stats.completed_this_week),1)])])])]),e("div",J,[e("div",K,[e("div",X,[t[13]||(t[13]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Recent Tasks",-1)),r(n(a),{href:o.route("tasks.index"),class:"text-sm text-blue-600 hover:text-blue-800"},{default:i(()=>t[12]||(t[12]=[x(" View All \u2192 ")])),_:1,__:[12]},8,["href"])]),e("div",ee,[(c(!0),g(m,null,v(d.recentTasks,s=>(c(),g("div",{key:s.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("div",te,[e("div",se,[e("span",{class:u(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",y(s.priority)])},l(s.priority),3),e("span",{class:u(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(s.type)])},l(p(s.type)),3)]),e("h4",oe,l(s.title),1),e("p",le," Assigned to: "+l(s.assigned_to.first_name)+" "+l(s.assigned_to.last_name),1)]),e("div",re,[e("div",ne,l(f(s.created_at)),1),r(n(a),{href:o.route("tasks.show",s.id),class:"text-xs text-blue-600 hover:text-blue-800"},{default:i(()=>t[14]||(t[14]=[x("View")])),_:2,__:[14]},1032,["href"])])]))),128)),d.recentTasks.length===0?(c(),g("div",ie," No recent tasks ")):_("",!0)])]),e("div",de,[e("div",ae,[t[16]||(t[16]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Upcoming Tasks",-1)),r(n(a),{href:o.route("tasks.index",{status:"pending"}),class:"text-sm text-blue-600 hover:text-blue-800"},{default:i(()=>t[15]||(t[15]=[x(" View All \u2192 ")])),_:1,__:[15]},8,["href"])]),e("div",ge,[(c(!0),g(m,null,v(d.upcomingTasks,s=>(c(),g("div",{key:s.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"},[e("div",ce,[e("div",xe,[e("span",{class:u(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",y(s.priority)])},l(s.priority),3),e("span",{class:u(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",b(s.type)])},l(p(s.type)),3)]),e("h4",ue,l(s.title),1),e("p",me," Assigned to: "+l(s.assigned_to.first_name)+" "+l(s.assigned_to.last_name),1)]),e("div",fe,[e("div",{class:u(["text-xs",{"text-red-600 font-semibold":h(s.due_date),"text-gray-700":!h(s.due_date)}])},l(f(s.due_date)),3),r(n(a),{href:o.route("tasks.show",s.id),class:"text-xs text-blue-600 hover:text-blue-800"},{default:i(()=>t[17]||(t[17]=[x("View")])),_:2,__:[17]},1032,["href"])])]))),128)),d.upcomingTasks.length===0?(c(),g("div",pe," No upcoming tasks ")):_("",!0)])])]),e("div",he,[t[22]||(t[22]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1)),e("div",be,[r(n(a),{href:o.route("tasks.create",{type:"call"}),class:"flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"},{default:i(()=>t[18]||(t[18]=[e("div",{class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"\u{1F4DE}")],-1),e("span",{class:"text-sm font-medium text-blue-900"},"Schedule Call",-1)])),_:1,__:[18]},8,["href"]),r(n(a),{href:o.route("tasks.create",{type:"follow_up"}),class:"flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"},{default:i(()=>t[19]||(t[19]=[e("div",{class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"\u{1F504}")],-1),e("span",{class:"text-sm font-medium text-green-900"},"Follow Up",-1)])),_:1,__:[19]},8,["href"]),r(n(a),{href:o.route("tasks.create",{type:"meeting"}),class:"flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors"},{default:i(()=>t[20]||(t[20]=[e("div",{class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"\u{1F91D}")],-1),e("span",{class:"text-sm font-medium text-purple-900"},"Schedule Meeting",-1)])),_:1,__:[20]},8,["href"]),r(n(a),{href:o.route("tasks.create",{type:"reminder"}),class:"flex flex-col items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors"},{default:i(()=>t[21]||(t[21]=[e("div",{class:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-2"},[e("span",{class:"text-2xl"},"\u23F0")],-1),e("span",{class:"text-sm font-medium text-yellow-900"},"Set Reminder",-1)])),_:1,__:[21]},8,["href"])])])])]),_:1})],64))}};export{we as default};
