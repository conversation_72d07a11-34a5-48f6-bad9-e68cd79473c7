import{c as A,d as i,a as _,Q as $,j as f,b as l,u as o,w as y,F as U,Z as F,e as n,x as P,p as u,f as d,r as E,y as x,m as I,t as O}from"./app.0e820f21.js";import{_ as Q,a as Y}from"./AdminLayout.687face1.js";import{_ as v,a as m}from"./TextInput.a134c4d6.js";import{_ as r}from"./InputLabel.c491b164.js";import{P as W}from"./PrimaryButton.259b896f.js";import{_ as H}from"./TextArea.3742605b.js";import{M as D}from"./Modal.c671de5e.js";import{_ as M}from"./SecondaryButton.f2b207b7.js";import{D as R}from"./DangerButton.7efeecc5.js";import{_ as Z}from"./SearchableDropdownNew.0cffdca8.js";import{u as G,_ as J}from"./index.bbe2b202.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const K={class:"w-full items-center"},X=["src"],ee=["src"],te={key:2},oe={__name:"FileViewer",props:{fileUrl:{type:String}},setup(c){const a=c,g=A(()=>{const e=a.fileUrl.split(".").pop().toLowerCase();return e==="pdf"?"pdf":["jpg","jpeg","png"].includes(e)?"image":"unsupported"});return(e,w)=>(i(),_("div",K,[g.value==="pdf"?(i(),_("iframe",{key:0,src:c.fileUrl,width:"100%",height:"500px",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,X)):g.value==="image"?(i(),_("img",{key:1,src:c.fileUrl,alt:"Image",style:{"max-width":"100%","max-height":"500px","overflow-y":"auto"}},null,8,ee)):(i(),_("p",te,"No Image"))]))}},se={class:"animate-top"},ne={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},le={class:"border-b border-gray-900/10 pb-12"},ie={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},ae={class:"sm:col-span-2"},de={class:"sm:col-span-2"},re={class:"relative mt-2"},me={class:"sm:col-span-2"},ue={class:"sm:col-span-2"},pe={class:"sm:col-span-2"},ve={class:"sm:col-span-2"},ye={class:"sm:col-span-2"},_e={class:"sm:col-span-2"},ce={class:"sm:col-span-2"},ge={class:"sm:col-span-2"},fe={class:"sm:col-span-2"},xe={class:"sm:col-span-1"},ke={class:"sm:col-span-1"},we={class:"sm:col-span-1"},Ve={class:"sm:col-span-1"},be={class:"sm:col-span-2"},qe={class:"sm:col-span-6"},Ce={key:0,class:"sm:col-span-6 bg-white p-1 shadow sm:rounded-lg border"},he={class:"min-w-full divide-y divide-gray-300"},$e={class:"divide-y divide-gray-300 bg-white"},Ue={class:"whitespace-nowrap py-2 pl-2 pr-3 text-sm font-medium text-gray-500 sm:pl-6"},De={class:"whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-500 sm:pl-6 space-x-2 items-center"},Me=["onClick"],Se=["onClick"],je=["onClick"],ze={class:"flex mt-6 items-center justify-between"},Le={class:"ml-auto flex items-center justify-end gap-x-6"},Ne={key:0,class:"text-sm text-gray-600"},Be={class:"p-6"},Te={class:"mt-6 flex justify-end"},Ae={class:"p-6"},Fe={class:"mt-6 px-4 flex justify-end"},et={__name:"Edit",props:{data:{type:Object},counties:{type:Array,required:!0},filepath:{type:String,required:!0}},setup(c){const a=$().props.data,g=$().props.filepath.view,e=G("post","/leads",{id:a.id,client_name:a.client_name,county_id:a.county_id,dimensions:a.dimensions,open_size:a.open_size,box_style:a.box_style,stock:a.stock,lamination:a.lamination,printing:a.printing,add_ons:a.add_ons,qty_1:a.qty_1,qty_2:a.qty_2,qty_3:a.qty_3,qty_4:a.qty_4,notes:a.notes,document:""}),w=()=>e.submit({preserveScroll:!0,onSuccess:()=>e.reset()}),S=(p,t)=>{e.county_id=p},j=p=>{e.document=p},V=f(!1),b=f(null),z=p=>{b.value=p,V.value=!0},L=()=>{e.get(route("removedocument",{id:b.value}),{onSuccess:()=>{closeDocumentModal()}})},k=f(!1),q=f(null),N=f("custom"),B=p=>{q.value=p,k.value=!0},C=()=>{k.value=!1},T=p=>{const t=window.location.origin+g+p,s=document.createElement("a");s.href=t,s.setAttribute("download",p),document.body.appendChild(s),s.click(),document.body.removeChild(s)};return(p,t)=>(i(),_(U,null,[l(o(F),{title:"Leads"}),l(Q,null,{default:y(()=>[n("div",se,[n("div",ne,[t[37]||(t[37]=n("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Lead",-1)),n("form",{onSubmit:P(w,["prevent"]),class:""},[n("div",le,[n("div",ie,[n("div",ae,[l(r,{for:"client_name",value:"Client Name *"}),l(v,{id:"client_name",type:"text",modelValue:o(e).client_name,"onUpdate:modelValue":t[0]||(t[0]=s=>o(e).client_name=s),required:"",onChange:t[1]||(t[1]=s=>o(e).validate("client_name"))},null,8,["modelValue"]),o(e).invalid("client_name")?(i(),u(m,{key:0,message:o(e).errors.client_name},null,8,["message"])):d("",!0)]),n("div",de,[l(r,{for:"county_id",value:"Country *"}),n("div",re,[l(Z,{options:c.counties,modelValue:o(e).county_id,"onUpdate:modelValue":t[2]||(t[2]=s=>o(e).county_id=s),onOnchange:S,required:"",placeholder:"Select Country"},null,8,["options","modelValue"])]),o(e).invalid("county_id")?(i(),u(m,{key:0,message:o(e).errors.county_id},null,8,["message"])):d("",!0)]),n("div",me,[l(r,{for:"email",value:"Email"}),l(v,{id:"email",type:"text",modelValue:o(e).email,"onUpdate:modelValue":t[3]||(t[3]=s=>o(e).email=s),onChange:t[4]||(t[4]=s=>o(e).validate("email"))},null,8,["modelValue"]),o(e).invalid("email")?(i(),u(m,{key:0,message:o(e).errors.email},null,8,["message"])):d("",!0)]),n("div",ue,[l(r,{for:"number",value:"Number"}),l(v,{id:"number",type:"text",modelValue:o(e).number,"onUpdate:modelValue":t[5]||(t[5]=s=>o(e).number=s),onChange:t[6]||(t[6]=s=>o(e).validate("number"))},null,8,["modelValue"]),o(e).invalid("number")?(i(),u(m,{key:0,message:o(e).errors.number},null,8,["message"])):d("",!0)]),n("div",pe,[l(r,{for:"dimensions",value:"Dimensions *"}),l(v,{id:"dimensions",type:"text",modelValue:o(e).dimensions,"onUpdate:modelValue":t[7]||(t[7]=s=>o(e).dimensions=s),required:"",onChange:t[8]||(t[8]=s=>o(e).validate("dimensions"))},null,8,["modelValue"]),o(e).invalid("dimensions")?(i(),u(m,{key:0,message:o(e).errors.dimensions},null,8,["message"])):d("",!0)]),n("div",ve,[l(r,{for:"open_size",value:"Open Size  *"}),l(v,{id:"open_size",type:"text",modelValue:o(e).open_size,"onUpdate:modelValue":t[9]||(t[9]=s=>o(e).open_size=s),required:"",onChange:t[10]||(t[10]=s=>o(e).validate("open_size"))},null,8,["modelValue"]),o(e).invalid("open_size")?(i(),u(m,{key:0,message:o(e).errors.open_size},null,8,["message"])):d("",!0)]),n("div",ye,[l(r,{for:"box_style",value:"Box Style *"}),l(v,{id:"box_style",type:"text",modelValue:o(e).box_style,"onUpdate:modelValue":t[11]||(t[11]=s=>o(e).box_style=s),required:"",onChange:t[12]||(t[12]=s=>o(e).validate("box_style"))},null,8,["modelValue"]),o(e).invalid("box_style")?(i(),u(m,{key:0,message:o(e).errors.box_style},null,8,["message"])):d("",!0)]),n("div",_e,[l(r,{for:"stock",value:"Stock *"}),l(v,{id:"stock",type:"text",modelValue:o(e).stock,"onUpdate:modelValue":t[13]||(t[13]=s=>o(e).stock=s),required:"",onChange:t[14]||(t[14]=s=>o(e).validate("stock"))},null,8,["modelValue"]),o(e).invalid("stock")?(i(),u(m,{key:0,message:o(e).errors.stock},null,8,["message"])):d("",!0)]),n("div",ce,[l(r,{for:"lamination",value:"Lamination *"}),l(v,{id:"lamination",type:"text",modelValue:o(e).lamination,"onUpdate:modelValue":t[15]||(t[15]=s=>o(e).lamination=s),required:"",onChange:t[16]||(t[16]=s=>o(e).validate("lamination"))},null,8,["modelValue"]),o(e).invalid("lamination")?(i(),u(m,{key:0,message:o(e).errors.lamination},null,8,["message"])):d("",!0)]),n("div",ge,[l(r,{for:"printing",value:"Printing *"}),l(v,{id:"printing",type:"text",modelValue:o(e).printing,"onUpdate:modelValue":t[17]||(t[17]=s=>o(e).printing=s),required:"",onChange:t[18]||(t[18]=s=>o(e).validate("printing"))},null,8,["modelValue"]),o(e).invalid("printing")?(i(),u(m,{key:0,message:o(e).errors.printing},null,8,["message"])):d("",!0)]),n("div",fe,[l(r,{for:"add_ons",value:"Add ons"}),l(v,{id:"add_ons",type:"text",modelValue:o(e).add_ons,"onUpdate:modelValue":t[19]||(t[19]=s=>o(e).add_ons=s),onChange:t[20]||(t[20]=s=>o(e).validate("add_ons"))},null,8,["modelValue"]),o(e).invalid("add_ons")?(i(),u(m,{key:0,message:o(e).errors.add_ons},null,8,["message"])):d("",!0)]),n("div",xe,[l(r,{for:"qty_1",value:"QTY 1 *"}),l(v,{id:"qty_1",type:"text",numeric:!0,modelValue:o(e).qty_1,"onUpdate:modelValue":t[21]||(t[21]=s=>o(e).qty_1=s),required:"",onChange:t[22]||(t[22]=s=>o(e).validate("qty_1"))},null,8,["modelValue"]),o(e).invalid("qty_1")?(i(),u(m,{key:0,message:o(e).errors.qty_1},null,8,["message"])):d("",!0)]),n("div",ke,[l(r,{for:"qty_2",value:"QTY 2"}),l(v,{id:"qty_2",type:"text",numeric:!0,modelValue:o(e).qty_2,"onUpdate:modelValue":t[23]||(t[23]=s=>o(e).qty_2=s),onChange:t[24]||(t[24]=s=>o(e).validate("qty_2"))},null,8,["modelValue"]),o(e).invalid("qty_2")?(i(),u(m,{key:0,message:o(e).errors.qty_2},null,8,["message"])):d("",!0)]),n("div",we,[l(r,{for:"qty_3",value:"QTY 3"}),l(v,{id:"qty_3",type:"text",numeric:!0,modelValue:o(e).qty_3,"onUpdate:modelValue":t[25]||(t[25]=s=>o(e).qty_3=s),onChange:t[26]||(t[26]=s=>o(e).validate("qty_3"))},null,8,["modelValue"]),o(e).invalid("qty_3")?(i(),u(m,{key:0,message:o(e).errors.qty_3},null,8,["message"])):d("",!0)]),n("div",Ve,[l(r,{for:"qty_4",value:"QTY 4"}),l(v,{id:"qty_4",type:"text",numeric:!0,modelValue:o(e).qty_4,"onUpdate:modelValue":t[27]||(t[27]=s=>o(e).qty_4=s),onChange:t[28]||(t[28]=s=>o(e).validate("qty_4"))},null,8,["modelValue"]),o(e).invalid("qty_4")?(i(),u(m,{key:0,message:o(e).errors.qty_4},null,8,["message"])):d("",!0)]),n("div",be,[l(r,{for:"note",value:"File Upload"}),l(J,{inputId:"document",inputName:"document",onFiles:j})]),n("div",qe,[l(r,{for:"notes",value:"Notes"}),l(H,{id:"notes",type:"text",rows:3,modelValue:o(e).notes,"onUpdate:modelValue":t[29]||(t[29]=s=>o(e).notes=s),autocomplete:"notes",onChange:t[30]||(t[30]=s=>o(e).validate("notes"))},null,8,["modelValue"]),l(m,{class:"",message:o(e).errors.notes},null,8,["message"])]),o(a).documents&&o(a).documents.length>0?(i(),_("div",Ce,[n("table",he,[t[34]||(t[34]=n("thead",{class:"bg-gray-50"},[n("tr",null,[n("th",{scope:"col",class:"py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"}," UPLOADED FILES "),n("th",{scope:"col",class:"px-3 py-3.5 text-left text-sm font-semibold text-gray-900"},"ACTION")])],-1)),n("tbody",$e,[(i(!0),_(U,null,E(o(a).documents,(s,Pe)=>(i(),_("tr",{key:o(a).id,class:""},[n("td",Ue,O(s.orignal_name),1),n("td",De,[n("button",{type:"button",onClick:h=>z(s.id)},t[31]||(t[31]=[n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)]),8,Me),n("button",{type:"button",onClick:h=>B(s.name)},t[32]||(t[32]=[n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)]),8,Se),n("button",{type:"button",onClick:h=>T(s.name)},t[33]||(t[33]=[n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6"},[n("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"1.5",d:"M12 4l-8 4v8l8 4 8-4V8l-8-4zm0 0v8m0 0 4-2m-4 2l-4-2m4 2V8"})],-1)]),8,je)])]))),128))])])])):d("",!0)])]),n("div",ze,[n("div",Le,[l(Y,{href:p.route("leads.index")},{svg:y(()=>t[35]||(t[35]=[n("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)])),_:1},8,["href"]),l(W,{disabled:o(e).processing},{default:y(()=>t[36]||(t[36]=[x("Save")])),_:1,__:[36]},8,["disabled"]),l(I,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:y(()=>[o(e).recentlySuccessful?(i(),_("p",Ne,"Saved.")):d("",!0)]),_:1})])])],32)])]),l(D,{show:V.value,onClose:p.closeDocumentModal},{default:y(()=>[n("div",Be,[t[40]||(t[40]=n("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this document? ",-1)),n("div",Te,[l(M,{onClick:p.closeDocumentModal},{default:y(()=>t[38]||(t[38]=[x(" Cancel")])),_:1,__:[38]},8,["onClick"]),l(R,{class:"ml-3",onClick:L},{default:y(()=>t[39]||(t[39]=[x(" Delete ")])),_:1,__:[39]})])])]),_:1},8,["show","onClose"]),l(D,{show:k.value,onClose:C,maxWidth:N.value},{default:y(()=>[n("div",Ae,[l(oe,{fileUrl:o(g)+q.value},null,8,["fileUrl"]),n("div",Fe,[l(M,{onClick:C},{default:y(()=>t[41]||(t[41]=[x(" Cancel")])),_:1,__:[41]})])])]),_:1},8,["show","maxWidth"])]),_:1})],64))}};export{et as default};
