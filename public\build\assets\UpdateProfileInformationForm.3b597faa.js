import{Q as x,T as v,a as d,e as o,b as a,u as e,l as y,B as b,y as u,w as c,v as V,f as _,m as h,x as w,d as g,q as k}from"./app.0e820f21.js";import{_ as m,a as n}from"./TextInput.a134c4d6.js";import{_ as i}from"./InputLabel.c491b164.js";import{P as U}from"./PrimaryButton.259b896f.js";import{_ as B}from"./TextArea.3742605b.js";import"./plugin-vue_export-helper.21dcd24c.js";const N={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},S={class:"sm:col-span-3"},$={class:"sm:col-span-3"},q={class:"sm:col-span-3"},C={class:"sm:col-span-3"},E={class:"sm:col-span-6"},P={class:"sm:col-span-6"},T={key:0},A={class:"text-sm mt-2 text-gray-800"},D={class:"mt-2 font-medium text-sm text-green-600"},F={class:"flex items-center gap-4"},I={key:0,class:"text-sm text-gray-600"},z={__name:"UpdateProfileInformationForm",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(f){const l=x().props.auth.user,t=v({first_name:l.first_name,last_name:l.last_name,contact_no:l.contact_no,email:l.email,address:l.address,dob:l.dob});return(p,s)=>(g(),d("section",null,[s[10]||(s[10]=o("header",null,[o("h2",{class:"text-lg font-medium text-gray-900"},"Profile Information"),o("p",{class:"text-sm text-gray-500"}," Update your account's profile information and email address. ")],-1)),o("form",{onSubmit:s[6]||(s[6]=w(r=>e(t).patch(p.route("profile.update")),["prevent"])),class:"mt-6 space-y-4"},[o("div",N,[o("div",S,[a(i,{for:"first_name",value:"First Name"}),a(m,{id:"first_name",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(t).first_name,"onUpdate:modelValue":s[0]||(s[0]=r=>e(t).first_name=r),required:"",autofocus:"",autocomplete:"first_name"},null,8,["modelValue"]),a(n,{class:"mt-2",message:e(t).errors.first_name},null,8,["message"])]),o("div",$,[a(i,{for:"last_name",value:"Last Name"}),a(m,{id:"last_name",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(t).last_name,"onUpdate:modelValue":s[1]||(s[1]=r=>e(t).last_name=r),required:"",autofocus:"",autocomplete:"last_name"},null,8,["modelValue"]),a(n,{class:"mt-2",message:e(t).errors.last_name},null,8,["message"])]),o("div",q,[a(i,{for:"email",value:"Email"}),a(m,{id:"email",type:"email",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(t).email,"onUpdate:modelValue":s[2]||(s[2]=r=>e(t).email=r),required:"",autocomplete:"username"},null,8,["modelValue"]),a(n,{class:"mt-2",message:e(t).errors.email},null,8,["message"])]),o("div",C,[a(i,{for:"contact_no",value:"Contact No"}),a(m,{id:"contact_no",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(t).contact_no,"onUpdate:modelValue":s[3]||(s[3]=r=>e(t).contact_no=r),autofocus:"",autocomplete:"contact_no"},null,8,["modelValue"]),a(n,{class:"mt-2",message:e(t).errors.contact_no},null,8,["message"])]),o("div",E,[a(i,{for:"dob",value:"DOB"}),y(o("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":s[4]||(s[4]=r=>e(t).dob=r)},null,512),[[b,e(t).dob]]),a(n,{class:"mt-2",message:e(t).errors.dob},null,8,["message"])]),o("div",P,[a(i,{for:"address",value:"Address"}),a(B,{id:"address",type:"text",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(t).address,"onUpdate:modelValue":s[5]||(s[5]=r=>e(t).address=r),autofocus:"",autocomplete:"address"},null,8,["modelValue"]),a(n,{class:"mt-2",message:e(t).errors.address},null,8,["message"])]),f.mustVerifyEmail&&e(l).email_verified_at===null?(g(),d("div",T,[o("p",A,[s[8]||(s[8]=u(" Your email address is unverified. ")),a(e(k),{href:p.route("verification.send"),method:"post",as:"button",class:"underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"},{default:c(()=>s[7]||(s[7]=[u(" Click here to re-send the verification email. ")])),_:1,__:[7]},8,["href"])]),y(o("div",D," A new verification link has been sent to your email address. ",512),[[V,f.status==="verification-link-sent"]])])):_("",!0)]),o("div",F,[a(U,{disabled:e(t).processing},{default:c(()=>s[9]||(s[9]=[u("Save")])),_:1,__:[9]},8,["disabled"]),a(h,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[e(t).recentlySuccessful?(g(),d("p",I,"Saved.")):_("",!0)]),_:1})])],32)]))}};export{z as default};
