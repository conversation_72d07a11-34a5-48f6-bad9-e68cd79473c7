import{T as ce,j as i,A as ue,h as G,i as R,c as ve,a as d,b as v,u as me,w as B,F as U,z as fe,d as c,Z as ge,e as t,l as Y,B as Z,f as y,r as q,g as pe,t as w,y as he}from"./app.0e820f21.js";import{_ as xe}from"./AdminLayout.687face1.js";import{_ as _e}from"./SecondaryButton.f2b207b7.js";import{_ as J}from"./SearchableDropdownNew.0cffdca8.js";import{_ as $}from"./InputLabel.c491b164.js";import{M as ye}from"./Modal.c671de5e.js";import{_ as we}from"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const be={class:"flex justify-between items-center mb-6"},ke={class:"ml-6 flex space-x-6 mt-4 sm:mt-0 w-64"},Ce={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full"},Me={class:"mt-4 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg"},Le={class:"flex mb-2"},$e={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12 items-center"},Fe={class:"sm:col-span-3"},Ve={class:"sm:col-span-3"},je={class:"sm:col-span-3"},Ae={class:"relative mt-2"},De={class:"sm:col-span-3"},Te={class:"relative mt-2"},Be={class:"mt-6 w-full"},Ue={key:0,class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"},Ne={class:"text-lg font-bold text-gray-800 mb-4"},Se={class:"relative px-4 py-4 bg-white rounded-lg shadow-md w-full"},Ee=["onClick"],Ie={class:"flex items-start space-x-4 items-center"},Oe={class:"w-10 h-10"},ze={key:0,xmlns:"http://www.w3.org/2000/svg",width:"40",height:"40",viewBox:"0 0 48 48",fill:"none"},He={key:1,width:"40",height:"40",xmlns:"http://www.w3.org/2000/svg"},Pe=["fill"],Ge={x:"50%",y:"50%","dominant-baseline":"middle","text-anchor":"middle",fill:"#FFF","font-size":"14","font-weight":"bold","font-family":"Arial, sans-serif"},Re={class:"flex flex-col justify-between w-full"},Ye={class:"flex items-start"},Ze={class:"text-gray-700"},qe=["innerHTML"],Je={key:1,class:"text-md font-semibold ml-1"},Ke={class:"text-sm text-gray-500"},Qe={class:"flex justify-center my-4"},We={key:0,class:"loader"},Xe={class:"p-6"},et=["innerHTML"],tt={class:"mt-4 flex justify-end"},st={__name:"ActivityLog",props:["activityLogs","user","userId","logNames"],setup(A){const D=A,K=ce({}),h=i(""),m=i(D.userId),Q=i("ALL Users"),g=i(""),p=i(""),f=i(""),b=i(null),x=i([...D.activityLogs]),N=i(!0),F=i(!1);ue(()=>D.activityLogs,o=>{x.value=[...o]});const W=async()=>{if(!N.value||F.value)return;F.value=!0;const e={offset:x.value.length,search:h.value,causer_id:m.value,from_date:g.value,to_date:p.value,log_name:f.value};try{const s=await fe.get(route("logs.loadMore"),{params:e}),n=s.data.activityLogs;x.value=x.value.concat(n),N.value=s.data.hasMore}catch(s){console.error("Error loading more logs",s)}finally{F.value=!1}};let V=null;G(()=>{V=new IntersectionObserver(o=>{o.forEach(e=>{e.isIntersecting&&W()})}),b.value&&V.observe(b.value)}),R(()=>{V&&b.value&&V.unobserve(b.value)});const k=(o,e,s,n,l)=>{h.value=o,K.get(route("logs",{search:o,causer_id:e,from_date:s,to_date:n,log_name:l}),{preserveState:!0})},X=()=>{k(h.value,m.value,g.value,p.value,f.value)},ee=()=>{k(h.value,m.value,g.value,p.value,f.value)},te=(o,e)=>{m.value=o,Q.value=e,k(h.value,m.value,g.value,p.value,f.value)},se=o=>{f.value=o,k(h.value,m.value,g.value,p.value,f.value)},oe=o=>{const e=new Date(o),s={year:"numeric",month:"short",day:"numeric"};return e.toLocaleDateString("en-US",s)},j={created:{color:"text-green-500",svgColor:"#22C55E"},updated:{color:"text-blue-500",svgColor:"#3B82F6"},deleted:{color:"text-red-500",svgColor:"#EF4444"},received:{color:"text-yellow-500",svgColor:"#F59E0B"},payment:{color:"text-purple-500",svgColor:"#8B5CF6"},default:{color:"text-gray-500",svgColor:"#9CA3AF"}},S=ve(()=>x.value.reduce((o,e)=>{const s=oe(e.created_at);return o[s]||(o[s]=[]),o[s].push(e),o},{})),le=o=>{var M;const e=o.properties||{},s=o.event||"",n=((M=j[s])==null?void 0:M.color)||j.default.color;if(!Object.keys(e).some(a=>e[a]&&Object.keys(e[a]).length>0))return"";let r="";return s==="deleted"&&e.old?(r+='<div class="space-y-1">',Object.keys(e.old).forEach(a=>{var L;const u=(L=e.old[a])!=null?L:"NA",_=`bg-red-100 ${n} px-3 py-1 rounded text-sm`;r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <span class="${_}">${u}</span>
        </div>
      `}),r+="</div>",r):(e.attributes&&(r+='<div class="space-y-1">',Object.keys(e.attributes).forEach(a=>{var z,H,P;const u=(H=(z=e.old)==null?void 0:z[a])!=null?H:null,_=(P=e.attributes[a])!=null?P:"NA",L=s==="updated"&&u===null?"NA":u,ie="bg-red-100 text-red-600 px-3 py-1 rounded text-sm",de="bg-green-100 text-green-600 px-3 py-1 rounded text-sm";r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <div class="flex items-center gap-2">
            ${s==="updated"?`<span class="${ie}">${L}</span>
                   <span class="text-gray-500 text-sm">\u2192</span>`:""}
            <span class="${de}">${_}</span>
          </div>
        </div>
      `}),r+="</div>"),Object.keys(e).forEach(a=>{if(a!=="attributes"&&a!=="old"){r===""&&(r+='<div class="space-y-1">');let u=e[a];typeof u=="string"&&(u=u.replace(/^"|"$/g,"")),r+=`
        <div class="flex justify-between items-center">
          <div class="${n} font-medium text-sm">${a}</div>
          <div class="bg-gray-100 text-gray-800 px-3 py-1 rounded text-sm">${u}</div>
        </div>
      `}}),r?`<div class="space-y-1">${r}</div>`:"")},T=i(!1),C=i(null),E=()=>{T.value=!1,C.value=null},ae=o=>{C.value=x.value.find(e=>e.id===o),T.value=!0},ne=(o="",e="")=>{const s=o.charAt(0).toUpperCase()||"",n=e.charAt(0).toUpperCase()||"";return`${s}${n}`||"U"},I=i(!1),re=()=>{window.scrollTo({top:0,behavior:"smooth"})},O=()=>{I.value=window.pageYOffset>300};return G(()=>{window.addEventListener("scroll",O)}),R(()=>{window.removeEventListener("scroll",O)}),(o,e)=>(c(),d(U,null,[v(me(ge),{title:"Audit Logs"}),v(xe,null,{default:B(()=>[t("div",be,[e[6]||(e[6]=t("h1",{class:"text-2xl font-semibold text-gray-900"},"Audit Logs",-1)),t("div",ke,[t("div",Ce,[e[5]||(e[5]=t("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[t("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),t("input",{id:"search-field",onInput:e[0]||(e[0]=s=>k(s.target.value,m.value,g.value,p.value,f.value)),class:"rounded-lg block h-full w-full border-0 pl-8 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm",placeholder:"Search...",type:"search",name:"search"},null,32)])])]),t("div",Me,[t("div",Le,[e[7]||(e[7]=t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),v($,{for:"customer_id",value:"Filters"})]),t("div",$e,[t("div",Fe,[v($,{for:"date",value:"From Date"}),Y(t("input",{"onUpdate:modelValue":e[1]||(e[1]=s=>g.value=s),class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:X},null,544),[[Z,g.value]])]),t("div",Ve,[v($,{for:"date",value:"To Date"}),Y(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>p.value=s),class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date",onChange:ee},null,544),[[Z,p.value]])]),t("div",je,[v($,{for:"log_name",value:"Task"}),t("div",Ae,[v(J,{options:[{id:"",name:"All Task"},...A.logNames.map(s=>({id:s,name:s}))],modelValue:f.value,"onUpdate:modelValue":e[3]||(e[3]=s=>f.value=s),onOnchange:se},null,8,["options","modelValue"])])]),t("div",De,[v($,{for:"customer_id",value:"Users"}),t("div",Te,[v(J,{options:A.user,modelValue:m.value,"onUpdate:modelValue":e[4]||(e[4]=s=>m.value=s),onOnchange:te},null,8,["options","modelValue"])])])])]),t("div",Be,[Object.keys(S.value).length===0?(c(),d("div",Ue," No activity logs found. ")):y("",!0),(c(!0),d(U,null,q(S.value,(s,n)=>(c(),d("div",{key:n,class:"mb-8"},[t("h2",Ne,w(n),1),(c(!0),d(U,null,q(s,l=>{var r,M,a,u,_;return c(),d("div",{key:l.id,class:"flex items-start mb-4"},[t("div",Se,[l.log_name!=="Number Setting Update"?(c(),d("button",{key:0,onClick:L=>ae(l.id),class:"absolute top-1/2 right-0 transform -translate-y-1/2 px-6 text-gray-500 hover:text-gray-700 rounded-full"},e[8]||(e[8]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-6 w-6","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})],-1)]),8,Ee)):y("",!0),t("div",Ie,[t("div",Oe,[l.event==="sent"?(c(),d("svg",ze,e[9]||(e[9]=[t("circle",{cx:"24",cy:"24",r:"22",fill:"url(#grad1)",stroke:"#28c62c","stroke-width":"3"},null,-1),t("g",{transform:"translate(10, 12)",stroke:"#FFFFFF","stroke-width":"3","stroke-linecap":"round","stroke-linejoin":"round"},[t("rect",{x:"2",y:"4",width:"24",height:"16",rx:"3",ry:"3",fill:"none"}),t("path",{d:"M3 5l10 10 12-10"})],-1),t("defs",null,[t("linearGradient",{id:"grad1",x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[t("stop",{offset:"0%",style:{"stop-color":"#3ADF5B","stop-opacity":"1"}}),t("stop",{offset:"100%",style:{"stop-color":"#28c62c","stop-opacity":"1"}})])],-1)]))):(c(),d("svg",He,[t("circle",{cx:"20",cy:"20",r:"20",fill:((r=j[l.event])==null?void 0:r.svgColor)||"#3B82F6"},null,8,Pe),t("text",Ge,w(ne((M=l.causer)==null?void 0:M.first_name,(a=l.causer)==null?void 0:a.last_name)),1)]))]),t("div",Re,[t("div",Ye,[t("p",Ze,[l.description?(c(),d("span",{key:0,class:"",innerHTML:l.description},null,8,qe)):y("",!0),l.log_name!=="PaymentPaid"&&l.log_name!=="Payment-Receive"&&l.log_name!=="Send-Invoice-Email"?(c(),d("span",Je,w(((u=l.causer)==null?void 0:u.first_name)||"Unknown")+" "+w(((_=l.causer)==null?void 0:_.last_name)||""),1)):y("",!0)])]),t("div",Ke,w(new Date(l.created_at).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})),1)])])])])}),128))]))),128)),t("div",Qe,[F.value?(c(),d("div",We)):y("",!0)]),t("div",{ref_key:"sentinel",ref:b,class:"h-1"},null,512)]),I.value?(c(),d("button",{key:0,onClick:re,class:"fixed bottom-5 right-5 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg"},e[10]||(e[10]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 15l7-7 7 7"})],-1)]))):y("",!0),v(ye,{show:T.value,onClose:E},{default:B(()=>{var s,n,l;return[t("div",Xe,[e[12]||(e[12]=t("h2",{class:"text-2xl font-semibold text-gray-800 mb-4"},"Details",-1)),t("p",{class:pe([((n=j[(s=C.value)==null?void 0:s.event])==null?void 0:n.color)||"text-gray-700","text-md mb-4 font-semibold"])},w((l=C.value)==null?void 0:l.event)+" details: ",3),t("div",{class:"overflow-y-auto p-4 mb-4 border rounded-lg shadow-sm bg-white",style:{"max-height":"340px"},innerHTML:le(C.value)},null,8,et),t("div",tt,[v(_e,{onClick:E},{default:B(()=>e[11]||(e[11]=[he("Close")])),_:1,__:[11]})])])]}),_:1},8,["show"])]),_:1})],64))}};var ut=we(st,[["__scopeId","data-v-60363d1b"]]);export{ut as default};
