import{d as a,a as i,e as s,g as B,j as C,T as q,b as o,u as l,w as n,F as V,Z as K,l as D,B as Z,K as G,y as m,f,r as L,p as S,t as p}from"./app.0e820f21.js";import{_ as J,b as Q,a as W}from"./AdminLayout.687face1.js";import{_ as P}from"./CreateButton.f13c50e2.js";import{_ as A}from"./SecondaryButton.f2b207b7.js";import{D as X}from"./DangerButton.7efeecc5.js";import{M as j}from"./Modal.c671de5e.js";import{s as Y,_ as ss}from"./sortAndSearch.29e714e8.js";import{_ as z,a as I}from"./TextInput.a134c4d6.js";import{_ as N}from"./InputLabel.c491b164.js";import{_ as es}from"./ArrowIcon.f2c2b1ba.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const ts=["aria-checked"],os={__name:"SwitchButton",props:["switchValue","userId"],emits:["updateSwitchValue"],setup(r,{emit:w}){const d=r,y=w,g=()=>{y("updateSwitchValue",!d.switchValue,d.userId)};return(M,x)=>(a(),i("button",{type:"button",class:B(["relative inline-flex items-center flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2",d.switchValue?"bg-indigo-600":"bg-gray-300","w-12 h-7 sm:w-14 sm:h-8"]),role:"switch","aria-checked":d.switchValue.toString(),onClick:g},[x[0]||(x[0]=s("span",{class:"sr-only"},"Toggle setting",-1)),s("span",{class:B(["inline-block transform rounded-full bg-white shadow transition duration-200 ease-in-out",d.switchValue?"translate-x-5 sm:translate-x-6":"translate-x-1","w-5 h-5 sm:w-6 sm:h-6"]),"aria-hidden":"true"},null,2)],10,ts))}},ls={class:"animate-top"},as={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},rs={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},ns={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},is={key:0,class:"sm:flex-none"},ds={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},us={class:"shadow rounded-lg"},cs={class:"w-full text-sm text-left rtl:text-right text-gray-500"},ms={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},fs={class:"border-b-2"},ps=["onClick"],ws={key:0},gs={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},xs={class:"px-4 py-2.5 min-w-36"},hs={class:"px-4 py-2.5 min-w-36"},ys={class:"items-center px-4 py-2.5"},_s={class:"items-center px-4 py-2.5"},bs={class:"flex items-center justify-start gap-4"},vs=["onClick"],ks=["onClick"],Cs={key:1},Vs={class:"p-6"},Ss={class:"mt-6 flex justify-end"},Ms={class:"p-6"},Us={class:"mt-6"},$s={class:"mt-4"},Bs={class:"mt-6 flex justify-end"},Ls={class:"ml-3 w-44"},qs={__name:"List",props:["data","search","permissions"],setup(r){const{form:w,search:d,sort:y,fetchData:g,sortKey:M,sortDirection:x}=Y("users.index"),_=C(!1),b=C(!1),h=C(null),u=q({password:"",password_confirmation:""}),R=[{field:"first_name",label:"NAME",sortable:!0,multiFieldSort:["first_name","last_name"]},{field:"email",label:"EMAIL",sortable:!0},{field:"role_id",label:"ROLE",sortable:!0},{field:"status",label:"STATUS",sortable:!1},{field:"action",label:"ACTION",sortable:!1}],T=c=>{h.value=c,_.value=!0},v=()=>{_.value=!1},k=()=>{b.value=!1,u.reset()},E=c=>{h.value=c,b.value=!0},F=()=>{w.delete(route("users.destroy",{id:h.value}),{onSuccess:()=>v()})},H=()=>{u.post(route("users.reset-password",{id:h.value}),{onSuccess:()=>{k()}})},O=(c,e)=>{w.post(route("users.activation",{id:e,status:c}),{})};return(c,e)=>(a(),i(V,null,[o(l(K),{title:"Users"}),o(J,null,{default:n(()=>[s("div",ls,[s("div",as,[e[6]||(e[6]=s("div",{class:"items-start"},[s("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Users")],-1)),s("div",rs,[s("div",ns,[e[4]||(e[4]=s("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[s("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),D(s("input",{type:"text","onUpdate:modelValue":e[0]||(e[0]=t=>G(d)?d.value=t:null),onInput:e[1]||(e[1]=(...t)=>l(g)&&l(g)(...t)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for users..."},null,544),[[Z,l(d)]])]),r.permissions.canCreateUser?(a(),i("div",is,[o(P,{href:c.route("users.create")},{default:n(()=>e[5]||(e[5]=[m(" Add User ")])),_:1,__:[5]},8,["href"])])):f("",!0)])]),s("div",ds,[s("div",us,[s("table",cs,[s("thead",ms,[s("tr",fs,[(a(),i(V,null,L(R,(t,U)=>s("th",{key:U,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:$=>l(y)(t.field,t.sortable)},[m(p(t.label)+" ",1),t.sortable?(a(),S(es,{key:0,isSorted:l(M)===t.field,direction:l(x)},null,8,["isSorted","direction"])):f("",!0)],8,ps)),64))])]),r.data.data&&r.data.data.length>0?(a(),i("tbody",ws,[(a(!0),i(V,null,L(r.data.data,(t,U)=>(a(),i("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[s("td",gs,p(t.first_name)+" "+p(t.last_name),1),s("td",xs,p(t.email),1),s("td",hs,p(t.roles[0].name),1),s("td",ys,[o(os,{switchValue:t.status,userId:t.id,onUpdateSwitchValue:O},null,8,["switchValue","userId"])]),s("td",_s,[s("div",bs,[o(Q,{align:"right",width:"48"},{trigger:n(()=>e[7]||(e[7]=[s("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[s("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[s("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)])),content:n(()=>[r.permissions.canEditUser?(a(),S(W,{key:0,href:c.route("users.edit",{id:t.id})},{svg:n(()=>e[8]||(e[8]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)])),text:n(()=>e[9]||(e[9]=[s("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)])),_:2},1032,["href"])):f("",!0),r.permissions.canResetPassword?(a(),i("button",{key:1,type:"button",onClick:$=>E(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},e[10]||(e[10]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z"})],-1),s("span",{class:"text-sm text-gray-700 leading-5"}," Reset Password ",-1)]),8,vs)):f("",!0),r.permissions.canDeleteUser?(a(),i("button",{key:2,type:"button",onClick:$=>T(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},e[11]||(e[11]=[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),s("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)]),8,ks)):f("",!0)]),_:2},1024)])])]))),128))])):(a(),i("tbody",Cs,e[12]||(e[12]=[s("tr",{class:"bg-white"},[s("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)])))])])]),r.data.data&&r.data.data.length>0?(a(),S(ss,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):f("",!0)]),o(j,{show:_.value,onClose:v},{default:n(()=>[s("div",Vs,[e[15]||(e[15]=s("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete? ",-1)),s("div",Ss,[o(A,{onClick:v},{default:n(()=>e[13]||(e[13]=[m(" Cancel ")])),_:1,__:[13]}),o(X,{class:"ml-3",onClick:F},{default:n(()=>e[14]||(e[14]=[m(" Delete ")])),_:1,__:[14]})])])]),_:1},8,["show"]),o(j,{show:b.value,onClose:k},{default:n(()=>[s("div",Ms,[e[18]||(e[18]=s("h2",{class:"text-lg font-medium text-gray-900"}," Reset User Password ",-1)),s("div",Us,[s("div",null,[o(N,{for:"password",value:"New Password"}),o(z,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:l(u).password,"onUpdate:modelValue":e[2]||(e[2]=t=>l(u).password=t),required:""},null,8,["modelValue"]),o(I,{class:"mt-2",message:l(u).errors.password},null,8,["message"])]),s("div",$s,[o(N,{for:"password_confirmation",value:"Confirm Password"}),o(z,{id:"password_confirmation",type:"password",class:"mt-1 block w-full",modelValue:l(u).password_confirmation,"onUpdate:modelValue":e[3]||(e[3]=t=>l(u).password_confirmation=t),required:""},null,8,["modelValue"]),o(I,{class:"mt-2",message:l(u).errors.password_confirmation},null,8,["message"])])]),s("div",Bs,[o(A,{onClick:k},{default:n(()=>e[16]||(e[16]=[m(" Cancel ")])),_:1,__:[16]}),s("div",Ls,[o(P,{onClick:H,disabled:l(u).processing},{default:n(()=>e[17]||(e[17]=[m(" Reset Password ")])),_:1,__:[17]},8,["disabled"])])])])]),_:1},8,["show"])]),_:1})],64))}};export{qs as default};
