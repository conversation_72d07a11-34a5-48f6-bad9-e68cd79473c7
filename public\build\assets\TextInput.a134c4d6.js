import{l as d,v as f,d as r,a as l,e as g,t as _,j as v,h}from"./app.0e820f21.js";const x={class:"text-sm text-red-600"},I={__name:"InputError",props:{message:{type:String}},setup(e){return(n,u)=>d((r(),l("div",null,[g("p",x,_(e.message),1)],512)),[[f,e.message]])}},y=["value"],k={__name:"TextInput",props:{modelValue:{required:!0},numeric:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{expose:n,emit:u}){const c=e,i=u,t=v(null),m=s=>s.replace(/\D/g,""),p=s=>{const a=s.target.value,o=c.numeric?m(a):a;o==""&&(t.value.value=""),i("update:modelValue",o)};return h(()=>{t.value.hasAttribute("autofocus")&&t.value.focus()}),n({focus:()=>t.value.focus()}),(s,a)=>(r(),l("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:e.modelValue,onInput:p,autocomplete:"off",ref_key:"input",ref:t},null,40,y))}};export{k as _,I as a};
