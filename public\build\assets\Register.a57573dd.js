import{T as f,p as y,w as n,d as m,b as t,u as s,Z as w,e as o,l as x,E as v,a as u,r as b,F as V,y as g,g as h,q as k,x as R,t as q}from"./app.0e820f21.js";import{G as U}from"./GuestLayout.5f1289af.js";import{_ as d,a as l}from"./TextInput.a134c4d6.js";import{_ as i}from"./InputLabel.c491b164.js";import{P as B}from"./PrimaryButton.259b896f.js";import{_ as N}from"./plugin-vue_export-helper.21dcd24c.js";const F={class:"mt-2 flex space-x-2"},L={class:"w-1/2"},P={class:"w-1/2"},C={class:"mt-2"},E={class:"mt-2"},S={class:"mt-2"},$={class:"mt-2"},A=["value"],D={class:"mt-4"},G={class:"mt-2 flex items-center space-x-1"},M={__name:"Register",props:{roles:{type:Array}},setup(p){const e=f({first_name:"",last_name:"",email:"",password:"",password_confirmation:"",role_id:""}),_=()=>{e.post(route("register"),{onFinish:()=>e.reset("password","password_confirmation")})};return(c,a)=>(m(),y(U,null,{default:n(()=>[t(s(w),{title:"Register"}),a[9]||(a[9]=o("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Register here",-1)),o("form",{onSubmit:R(_,["prevent"])},[o("div",F,[o("div",L,[t(i,{for:"first_name",value:"First Name"}),t(d,{id:"first_name",type:"text",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:s(e).first_name,"onUpdate:modelValue":a[0]||(a[0]=r=>s(e).first_name=r),required:"",autofocus:"",autocomplete:"first_name"},null,8,["modelValue"]),t(l,{class:"mt-2",message:s(e).errors.first_name},null,8,["message"])]),o("div",P,[t(i,{for:"last_name",value:"Last Name"}),t(d,{id:"last_name",type:"text",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:s(e).last_name,"onUpdate:modelValue":a[1]||(a[1]=r=>s(e).last_name=r),required:"",autofocus:"",autocomplete:"last_name"},null,8,["modelValue"]),t(l,{class:"mt-2",message:s(e).errors.last_name},null,8,["message"])])]),o("div",C,[t(i,{for:"email",value:"Email"}),t(d,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:s(e).email,"onUpdate:modelValue":a[2]||(a[2]=r=>s(e).email=r),required:"",autocomplete:"username"},null,8,["modelValue"]),t(l,{class:"mt-2",message:s(e).errors.email},null,8,["message"])]),o("div",E,[t(i,{for:"password",value:"Password"}),t(d,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:s(e).password,"onUpdate:modelValue":a[3]||(a[3]=r=>s(e).password=r),required:"",autocomplete:"new-password"},null,8,["modelValue"]),t(l,{class:"mt-2",message:s(e).errors.password},null,8,["message"])]),o("div",S,[t(i,{for:"password_confirmation",value:"Confirm Password"}),t(d,{id:"password_confirmation",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:s(e).password_confirmation,"onUpdate:modelValue":a[4]||(a[4]=r=>s(e).password_confirmation=r),required:"",autocomplete:"new-password"},null,8,["modelValue"]),t(l,{class:"mt-2",message:s(e).errors.password_confirmation},null,8,["message"])]),o("div",$,[t(i,{for:"role_id",value:"Role"}),x(o("select",{"onUpdate:modelValue":a[5]||(a[5]=r=>s(e).role_id=r),class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},[(m(!0),u(V,null,b(p.roles,r=>(m(),u("option",{value:r.id,key:r.id},q(r.name),9,A))),128))],512),[[v,s(e).role_id]]),t(l,{class:"mt-2",message:s(e).errors.role_id},null,8,["message"])]),o("div",D,[t(B,{class:h(["",{"opacity-25":s(e).processing}]),disabled:s(e).processing},{default:n(()=>a[6]||(a[6]=[g(" Register ")])),_:1,__:[6]},8,["class","disabled"])]),o("div",G,[a[8]||(a[8]=o("span",{class:"text-sm text-gray-700"},"Already have an account ?",-1)),t(s(k),{href:c.route("login"),class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:n(()=>a[7]||(a[7]=[g("Login ")])),_:1,__:[7]},8,["href"])])],32)]),_:1,__:[9]}))}};var J=N(M,[["__scopeId","data-v-1ba0f6be"]]);export{J as default};
