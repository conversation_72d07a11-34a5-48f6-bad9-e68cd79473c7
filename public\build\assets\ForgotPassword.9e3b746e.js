import{T as n,p as u,w as l,d as i,b as a,u as t,Z as p,e as o,a as _,t as c,f,y as g,g as y,x as w}from"./app.0e820f21.js";import{G as x}from"./GuestLayout.5f1289af.js";import{_ as b,a as v}from"./TextInput.a134c4d6.js";import{_ as k}from"./InputLabel.c491b164.js";import{P}from"./PrimaryButton.259b896f.js";import{_ as V}from"./plugin-vue_export-helper.21dcd24c.js";const B={key:0,class:"mb-4 font-medium text-sm text-green-600"},F={class:"flex items-center justify-end mt-4"},N={__name:"ForgotPassword",props:{status:{type:String}},setup(r){const s=n({email:""}),m=()=>{s.post(route("password.email"))};return(h,e)=>(i(),u(x,null,{default:l(()=>[a(t(p),{title:"Forgot Password"}),e[2]||(e[2]=o("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"}," Forgot your password ?",-1)),e[3]||(e[3]=o("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),r.status?(i(),_("div",B,c(r.status),1)):f("",!0),o("form",{onSubmit:w(m,["prevent"])},[o("div",null,[a(k,{for:"email",value:"Email"}),a(b,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:t(s).email,"onUpdate:modelValue":e[0]||(e[0]=d=>t(s).email=d),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),a(v,{class:"mt-2",message:t(s).errors.email},null,8,["message"])]),o("div",F,[a(P,{class:y({"opacity-25":t(s).processing}),disabled:t(s).processing},{default:l(()=>e[1]||(e[1]=[g(" Email Password Reset Link ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2,3]}))}};var T=V(N,[["__scopeId","data-v-87adb586"]]);export{T as default};
