import{G as n,a as i,e,b as s,w as l,u as o,d as c,q as _,Z as p}from"./app.0e820f21.js";import{_ as d}from"./plugin-vue_export-helper.21dcd24c.js";const u={class:"min-h-screen loginview flex flex-col sm:justify-center items-center pt-6 sm:pt-0 bg-slate-100"},m={class:"container shadow-md"},v=["href"],f={__name:"Registerv2",setup(g){return(r,t)=>{const a=n("ApplicationLogo");return c(),i("div",u,[e("div",null,[s(o(_),{href:"/"},{default:l(()=>[s(a,{class:"w-60 fill-current text-gray-500"})]),_:1})]),s(o(p),{title:"Register"}),e("div",m,[t[0]||(t[0]=e("h2",null,"Access Denied",-1)),t[1]||(t[1]=e("p",{class:"text-sm text-gray-500"},"You do not have permission to access this page/event. ",-1)),t[2]||(t[2]=e("p",{class:"text-sm text-gray-500"},"Please contact your administrator if you believe this is an error.",-1)),e("h4",null,[e("a",{href:r.route("login")},"Return to Login",8,v)])])])}}};var y=d(f,[["__scopeId","data-v-657e3910"]]);export{y as default};
