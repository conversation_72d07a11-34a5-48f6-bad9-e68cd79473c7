import{d as a,a as n,e as _,F as m,r as x,b as h,u as v,q as p,g as b,f as w,T as L,j as c}from"./app.0e820f21.js";const P={key:0},k={class:"flex flex-wrap justify-end isolate rounded-md"},T={key:0},H={key:1},D={__name:"Pagination",props:["links"],setup(o){return(g,l)=>o.links.length>1?(a(),n("div",P,[_("div",k,[(a(!0),n(m,null,x(o.links,(e,t)=>(a(),n(m,{key:t},[e.url===null?(a(),n("div",T,[h(v(p),{innerHTML:e.label,href:"#",class:"inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 bg-white hover:bg-gray-50 focus:z-20 focus:outline-offset-0"},null,8,["innerHTML"])])):(a(),n("div",H,[h(v(p),{innerHTML:e.label,href:e.url,class:b([{"bg-indigo-600 text-white hover:bg-indigo-600":e.active},"bg-white inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"])},null,8,["innerHTML","href","class"])]))],64))),128))])])):w("",!0)}};function N(o,g={}){const l=L({}),e=c(""),t=c("id"),r=c("desc"),u=c({...g}),y=s=>{u.value={...u.value,...s}},f=(s={})=>{const i={...u.value,...s,search:e.value,sort_by:t.value,sort_direction:r.value},d=new URLSearchParams(window.location.search).get("page");d&&(i.page=d),l.get(route(o,i),{preserveState:!0,replace:!0})};return{form:l,search:e,sort:(s,i=!0)=>{i&&(t.value===s?r.value=r.value==="asc"?"desc":"asc":(r.value="asc",t.value=s),f())},fetchData:f,sortKey:t,sortDirection:r,updateParams:y}}export{D as _,N as s};
