
<script setup>
import { ref } from 'vue'
import { Head, Link, useForm } from '@inertiajs/vue3';
import AdminLayout from '@/Layouts/AdminLayout.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import TextArea from '@/Components/TextArea.vue';
import InputError from '@/Components/InputError.vue';
import SearchableDropdownNew from '@/Components/SearchableDropdownNew.vue';
import SvgLink from '@/Components/ActionLink.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

const props = defineProps({
    users: Array,
    types: Array,
    priority: Array,
    leads: Array
})

const form = useForm({
    title: '',
    description: '',
    type: '',
    priority: 'medium',
    due_date: '',
    reminder_date: '',
    assigned_to: '',
    lead_id: props.lead ? props.lead.id : null,
    notes: ''
})

// Set default due date to tomorrow 9 AM
const tomorrow = new Date()
tomorrow.setDate(tomorrow.getDate() + 1)
tomorrow.setHours(9, 0, 0, 0)
form.due_date = tomorrow.toISOString().slice(0, 16)

// Set default reminder to 1 hour before due date
const reminderTime = new Date(tomorrow)
reminderTime.setHours(8, 0, 0, 0)
form.reminder_date = reminderTime.toISOString().slice(0, 16)

const setQuickTask = (type, title, priority) => {
    form.type = type
    form.title = title
    form.priority = priority
}

const setLead = (id, name) => {
    form.lead_id = id;
};

const setType = (id, name) => {
    form.type = id
}

const setPriority = (id, name) => {
    form.priority = id
}

const setAssigned = (id, name) => {
    form.assigned_to = id;
};

const submit = () => {
    form.post(route('tasks.store'), {
        preserveScroll: true,
    })
}
</script>

<template>
    <Head title="Tasks" />

    <AdminLayout>
        <div class="animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">Create New Task</h2>
                    <p class="text-sm text-gray-600 mt-1">Schedule a follow-up or reminder</p>
                </div>
                <Link :href="route('tasks.index')"
                      class="px-4 py-2 bg-slate-50 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100">
                    ← Back to Tasks
                </Link>
            </div>

            <!-- Form -->
            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-12 gap-6">
                    <div class="md:col-span-3">
                        <InputLabel for="lead_id" value="Leads" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="leads"
                            v-model="form.lead_id"
                            @onchange="setLead"
                            />
                        </div>
                    </div>

                    <!-- Title -->
                    <div class="md:col-span-3">
                        <InputLabel for="title" value="Task Title *" />
                        <TextInput
                            id="title"
                            type="text"
                            v-model="form.title"
                            class="mt-1 block w-full"
                            placeholder="e.g., Call John about quotation follow-up"
                            required
                        />
                        <InputError :message="form.errors.title" />
                    </div>
                    <div class="md:col-span-3">
                        <InputLabel for="type" value="Task Type *" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="types"
                            v-model="form.type"
                            @onchange="setType"
                            />
                        </div>
                        <InputError :message="form.errors.type" />
                    </div>

                    <div class="md:col-span-3">
                        <InputLabel for="priority" value="Priority *" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="priority"
                            v-model="form.priority"
                            @onchange="setPriority"
                            />
                        </div>
                        <InputError :message="form.errors.priority" />
                    </div>

                    <!-- Description -->
                    <div class="md:col-span-6">
                        <InputLabel for="description" value="Description" />
                        <TextArea
                            id="description"
                            type="text"
                            :rows="3"
                            v-model="form.description"
                            autocomplete="description"
                            @change="form.validate('description')"
                        />
                        <InputError :message="form.errors.description" />
                    </div>

                    <!-- Notes -->
                    <div class="md:col-span-6">
                        <InputLabel for="notes" value="Additional Notes" />
                        <TextArea
                            id="notes"
                            type="text"
                            :rows="3"
                            v-model="form.notes"
                            placeholder="Any additional notes or instructions..."
                        />
                        <InputError :message="form.errors.notes" />
                    </div>

                    <!-- Due Date -->
                    <div class="md:col-span-4">
                        <InputLabel for="due_date" value="Due Date & Time *" />
                        <TextInput
                            id="due_date"
                            type="datetime-local"
                            v-model="form.due_date"
                            class="mt-1 block w-full"
                            required
                        />
                        <InputError :message="form.errors.due_date" />
                    </div>

                    <!-- Reminder Date -->
                    <div class="md:col-span-4">
                        <InputLabel for="reminder_date" value="Reminder Date & Time" />
                        <TextInput
                            id="reminder_date"
                            type="datetime-local"
                            v-model="form.reminder_date"
                            class="mt-1 block w-full"
                        />
                        <InputError :message="form.errors.reminder_date" />
                        <p class="text-xs text-gray-500 mt-1">Optional: Get reminded before the due date</p>
                    </div>

                    <!-- Assigned To -->
                    <div class="md:col-span-4">
                         <InputLabel for="assigned_to" value="Assign To *" />
                        <div class="relative mt-2">
                            <SearchableDropdownNew :options="users"
                            v-model="form.assigned_to"
                            @onchange="setAssigned"
                            />
                        </div>
                        <InputError :message="form.errors.assigned_to" />
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Quick Actions:</h4>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <button type="button" @click="setQuickTask('call', 'Call client', 'high')"
                                class="px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">
                            📞 Schedule Call
                        </button>
                        <button type="button" @click="setQuickTask('follow_up', 'Follow up on quotation', 'medium')"
                                class="px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                            💰 Quote Follow-up
                        </button>
                        <button type="button" @click="setQuickTask('meeting', 'Schedule meeting', 'high')"
                                class="px-3 py-2 text-xs bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200">
                            🤝 Schedule Meeting
                        </button>
                        <button type="button" @click="setQuickTask('reminder', 'Follow up reminder', 'low')"
                                class="px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200">
                            ⏰ Set Reminder
                        </button>
                    </div>
                </div>
                <div class="flex mt-6 items-center justify-between">
                    <div class="ml-auto flex items-center justify-end gap-x-6">
                        <SvgLink :href="route('tasks.index')">
                            <template #svg>
                                <button type="button" class="text-sm font-semibold leading-6 text-gray-900">
                                    Cancel
                                </button>
                            </template>
                        </SvgLink>
                    <PrimaryButton :disabled="form.processing">Save</PrimaryButton>
                    <Transition
                        enter-active-class="transition ease-in-out"
                        enter-from-class="opacity-0"
                        leave-active-class="transition ease-in-out"
                        leave-to-class="opacity-0"
                    >
                        <p v-if="form.recentlySuccessful" class="text-sm text-gray-600">Saved.</p>
                    </Transition>
                    </div>
                </div>
            </form>
        </div>
    </AdminLayout>
</template>

