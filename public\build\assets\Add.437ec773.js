import{T as g,a as u,b as e,u as a,w as i,F as v,d as c,Z as y,e as t,x as V,l as x,B as b,g as w,y as U,m as h,f as N}from"./app.0e820f21.js";import{_ as $,a as B}from"./AdminLayout.687face1.js";import{_ as n,a as r}from"./TextInput.a134c4d6.js";import{_ as d}from"./InputLabel.c491b164.js";import{P as S}from"./PrimaryButton.259b896f.js";import{_ as q}from"./TextArea.3742605b.js";import{_ as C}from"./SearchableDropdown.b12abea6.js";import"./plugin-vue_export-helper.21dcd24c.js";const k={class:"animate-top"},A={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},T={class:"border-b border-gray-900/10 pb-12"},F={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-6"},P={class:"sm:col-span-2"},j={class:"sm:col-span-2"},D={class:"sm:col-span-2"},E={class:"sm:col-span-3"},L={class:"sm:col-span-3"},M={class:"sm:col-span-3"},R={class:"sm:col-span-3"},z={class:"relative mt-2"},O={class:"sm:col-span-6"},Z={class:"flex mt-6 items-center justify-between"},G={class:"ml-auto flex items-center justify-end gap-x-6"},H={key:0,class:"text-sm text-gray-600"},es={__name:"Add",props:{roles:{type:Array}},setup(p){const s=g({role_id:"",first_name:"",last_name:"",email:"",contact_no:"",dob:"",address:"",password:""}),_=()=>s.post(route("users.store"),{preserveScroll:!0,onSuccess:()=>s.reset()}),f=(m,o)=>{s.role_id=m};return(m,o)=>(c(),u(v,null,[e(a(y),{title:"Users"}),e($,null,{default:i(()=>[t("div",k,[t("div",A,[o[10]||(o[10]=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New User",-1)),t("form",{onSubmit:V(_,["prevent"]),class:""},[t("div",T,[t("div",F,[t("div",P,[e(d,{for:"first_name",value:"First Name *"}),e(n,{id:"first_name",type:"text",modelValue:a(s).first_name,"onUpdate:modelValue":o[0]||(o[0]=l=>a(s).first_name=l),required:""},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.first_name},null,8,["message"])]),t("div",j,[e(d,{for:"last_name",value:"Last Name *"}),e(n,{id:"last_name",type:"text",modelValue:a(s).last_name,"onUpdate:modelValue":o[1]||(o[1]=l=>a(s).last_name=l),required:""},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.last_name},null,8,["message"])]),t("div",D,[e(d,{for:"dob",value:"Date of Birth"}),x(t("input",{class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",type:"date","onUpdate:modelValue":o[2]||(o[2]=l=>a(s).dob=l)},null,512),[[b,a(s).dob]]),e(r,{class:"",message:a(s).errors.dob},null,8,["message"])]),t("div",E,[e(d,{for:"email",value:"Email *"}),e(n,{id:"email",type:"email",modelValue:a(s).email,"onUpdate:modelValue":o[3]||(o[3]=l=>a(s).email=l),required:""},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.email},null,8,["message"])]),t("div",L,[e(d,{for:"password",value:"Password *"}),e(n,{id:"password",type:"password",modelValue:a(s).password,"onUpdate:modelValue":o[4]||(o[4]=l=>a(s).password=l),required:""},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.password},null,8,["message"])]),t("div",M,[e(d,{for:"contact_no",value:"Contact No"}),e(n,{id:"contact_no",type:"text",numeric:!0,maxLength:"10",modelValue:a(s).contact_no,"onUpdate:modelValue":o[5]||(o[5]=l=>a(s).contact_no=l)},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.contact_no},null,8,["message"])]),t("div",R,[e(d,{for:"role_id",value:"Role *"}),t("div",z,[e(C,{options:p.roles,modelValue:a(s).role_id,"onUpdate:modelValue":o[6]||(o[6]=l=>a(s).role_id=l),onOnchange:f,required:"",class:w({"error rounded-md":a(s).errors.company_id})},null,8,["options","modelValue","class"])]),e(r,{class:"",message:a(s).errors.role_id},null,8,["message"])]),t("div",O,[e(d,{for:"address",value:"Address"}),e(q,{id:"address",type:"text",rows:4,modelValue:a(s).address,"onUpdate:modelValue":o[7]||(o[7]=l=>a(s).address=l)},null,8,["modelValue"]),e(r,{class:"",message:a(s).errors.address},null,8,["message"])])])]),t("div",Z,[t("div",G,[e(B,{href:m.route("users.index")},{svg:i(()=>o[8]||(o[8]=[t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),e(S,{disabled:a(s).processing},{default:i(()=>o[9]||(o[9]=[U("Save")])),_:1,__:[9]},8,["disabled"]),e(h,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:i(()=>[a(s).recentlySuccessful?(c(),u("p",H,"Saved.")):N("",!0)]),_:1})])])],32)])])]),_:1})],64))}};export{es as default};
