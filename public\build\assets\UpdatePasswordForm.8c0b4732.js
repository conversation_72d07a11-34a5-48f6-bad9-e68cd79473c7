import{j as i,T as g,a as p,e as o,b as e,u as r,w as c,m as _,x as y,d as w,y as x,f as v}from"./app.0e820f21.js";import{_ as n,a as d}from"./TextInput.a134c4d6.js";import{_ as l}from"./InputLabel.c491b164.js";import{P as V}from"./PrimaryButton.259b896f.js";import"./plugin-vue_export-helper.21dcd24c.js";const b={class:"flex items-center gap-4"},P={key:0,class:"text-sm text-gray-600"},h={__name:"UpdatePasswordForm",setup(k){const u=i(null),m=i(null),s=g({current_password:"",password:"",password_confirmation:""}),f=()=>{s.put(route("password.update"),{preserveScroll:!0,onSuccess:()=>s.reset(),onError:()=>{s.errors.password&&(s.reset("password","password_confirmation"),u.value.focus()),s.errors.current_password&&(s.reset("current_password"),m.value.focus())}})};return(S,a)=>(w(),p("section",null,[a[4]||(a[4]=o("header",null,[o("h2",{class:"text-lg font-medium text-gray-900"},"Update Password"),o("p",{class:"text-sm text-gray-500"}," Ensure your account is using a long, random password to stay secure. ")],-1)),o("form",{onSubmit:y(f,["prevent"]),class:"mt-6 space-y-4"},[o("div",null,[e(l,{for:"current_password",value:"Current Password"}),e(n,{id:"current_password",ref_key:"currentPasswordInput",ref:m,modelValue:r(s).current_password,"onUpdate:modelValue":a[0]||(a[0]=t=>r(s).current_password=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"current-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.current_password,class:"mt-2"},null,8,["message"])]),o("div",null,[e(l,{for:"password",value:"New Password"}),e(n,{id:"password",ref_key:"passwordInput",ref:u,modelValue:r(s).password,"onUpdate:modelValue":a[1]||(a[1]=t=>r(s).password=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"new-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.password,class:"mt-2"},null,8,["message"])]),o("div",null,[e(l,{for:"password_confirmation",value:"Confirm Password"}),e(n,{id:"password_confirmation",modelValue:r(s).password_confirmation,"onUpdate:modelValue":a[2]||(a[2]=t=>r(s).password_confirmation=t),type:"password",class:"mt-1 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",autocomplete:"new-password"},null,8,["modelValue"]),e(d,{message:r(s).errors.password_confirmation,class:"mt-2"},null,8,["message"])]),o("div",b,[e(V,{disabled:r(s).processing},{default:c(()=>a[3]||(a[3]=[x("Save")])),_:1,__:[3]},8,["disabled"]),e(_,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:c(()=>[r(s).recentlySuccessful?(w(),p("p",P,"Saved.")):v("",!0)]),_:1})])],32)]))}};export{h as default};
