import{T as S,a as f,b as s,u as l,w as u,F as C,d as v,Z as $,e as o,q as A,y as _,x as U,m as D,f as N}from"./app.0e820f21.js";import{_ as O,a as q}from"./AdminLayout.687face1.js";import{_ as n}from"./InputLabel.c491b164.js";import{_ as b,a as d}from"./TextInput.a134c4d6.js";import{_ as x}from"./TextArea.3742605b.js";import{_ as g}from"./SearchableDropdownNew.0cffdca8.js";import{P as B}from"./PrimaryButton.259b896f.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},P={class:"flex justify-between items-center mb-6"},j={class:"grid grid-cols-1 md:grid-cols-12 gap-6"},Q={class:"md:col-span-3"},H={class:"relative mt-2"},I={class:"md:col-span-3"},L={class:"md:col-span-3"},M={class:"relative mt-2"},E={class:"md:col-span-3"},J={class:"relative mt-2"},R={class:"md:col-span-6"},Z={class:"md:col-span-6"},z={class:"md:col-span-4"},G={class:"md:col-span-4"},K={class:"relative mt-2"},W={class:"bg-gray-50 p-4 rounded-lg"},X={class:"grid grid-cols-2 md:grid-cols-4 gap-2"},Y={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},te={key:0,class:"text-sm text-gray-600"},me={__name:"Create",props:{users:Array,types:Array,priority:Array,leads:Array},setup(r){const c=r,t=S({title:"",description:"",type:"",priority:"medium",due_date:"",reminder_date:"",assigned_to:"",lead_id:c.lead?c.lead.id:null,notes:""}),m=new Date;m.setDate(m.getDate()+1),m.setHours(9,0,0,0),t.due_date=m.toISOString().slice(0,16);const y=new Date(m);y.setHours(8,0,0,0),t.reminder_date=y.toISOString().slice(0,16);const p=(i,e,a)=>{t.type=i,t.title=e,t.priority=a},V=(i,e)=>{t.lead_id=i},w=(i,e)=>{t.type=i},k=(i,e)=>{t.priority=i},h=(i,e)=>{t.assigned_to=i},T=()=>{t.post(route("tasks.store"),{preserveScroll:!0})};return(i,e)=>(v(),f(C,null,[s(l($),{title:"Tasks"}),s(O,null,{default:u(()=>[o("div",F,[o("div",P,[e[14]||(e[14]=o("div",null,[o("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Create New Task"),o("p",{class:"text-sm text-gray-600 mt-1"},"Schedule a follow-up or reminder")],-1)),s(l(A),{href:i.route("tasks.index"),class:"px-4 py-2 bg-slate-50 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:u(()=>e[13]||(e[13]=[_(" \u2190 Back to Tasks ")])),_:1,__:[13]},8,["href"])]),o("form",{onSubmit:U(T,["prevent"]),class:"space-y-6"},[o("div",j,[o("div",Q,[s(n,{for:"lead_id",value:"Leads"}),o("div",H,[s(g,{options:r.leads,modelValue:l(t).lead_id,"onUpdate:modelValue":e[0]||(e[0]=a=>l(t).lead_id=a),onOnchange:V},null,8,["options","modelValue"])])]),o("div",I,[s(n,{for:"title",value:"Task Title *"}),s(b,{id:"title",type:"text",modelValue:l(t).title,"onUpdate:modelValue":e[1]||(e[1]=a=>l(t).title=a),class:"mt-1 block w-full",placeholder:"e.g., Call John about quotation follow-up",required:""},null,8,["modelValue"]),s(d,{message:l(t).errors.title},null,8,["message"])]),o("div",L,[s(n,{for:"type",value:"Task Type *"}),o("div",M,[s(g,{options:r.types,modelValue:l(t).type,"onUpdate:modelValue":e[2]||(e[2]=a=>l(t).type=a),onOnchange:w},null,8,["options","modelValue"])]),s(d,{message:l(t).errors.type},null,8,["message"])]),o("div",E,[s(n,{for:"priority",value:"Priority *"}),o("div",J,[s(g,{options:r.priority,modelValue:l(t).priority,"onUpdate:modelValue":e[3]||(e[3]=a=>l(t).priority=a),onOnchange:k},null,8,["options","modelValue"])]),s(d,{message:l(t).errors.priority},null,8,["message"])]),o("div",R,[s(n,{for:"description",value:"Description"}),s(x,{id:"description",type:"text",rows:3,modelValue:l(t).description,"onUpdate:modelValue":e[4]||(e[4]=a=>l(t).description=a),autocomplete:"description",onChange:e[5]||(e[5]=a=>l(t).validate("description"))},null,8,["modelValue"]),s(d,{message:l(t).errors.description},null,8,["message"])]),o("div",Z,[s(n,{for:"notes",value:"Additional Notes"}),s(x,{id:"notes",type:"text",rows:3,modelValue:l(t).notes,"onUpdate:modelValue":e[6]||(e[6]=a=>l(t).notes=a),placeholder:"Any additional notes or instructions..."},null,8,["modelValue"]),s(d,{message:l(t).errors.notes},null,8,["message"])]),o("div",z,[s(n,{for:"due_date",value:"Due Date & Time *"}),s(b,{id:"due_date",type:"datetime-local",modelValue:l(t).due_date,"onUpdate:modelValue":e[7]||(e[7]=a=>l(t).due_date=a),class:"mt-1 block w-full",required:""},null,8,["modelValue"]),s(d,{message:l(t).errors.due_date},null,8,["message"])]),o("div",G,[s(n,{for:"assigned_to",value:"Assign To *"}),o("div",K,[s(g,{options:r.users,modelValue:l(t).assigned_to,"onUpdate:modelValue":e[8]||(e[8]=a=>l(t).assigned_to=a),onOnchange:h},null,8,["options","modelValue"])]),s(d,{message:l(t).errors.assigned_to},null,8,["message"])])]),o("div",W,[e[15]||(e[15]=o("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Quick Actions:",-1)),o("div",X,[o("button",{type:"button",onClick:e[9]||(e[9]=a=>p("call","Call client","high")),class:"px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"}," \u{1F4DE} Schedule Call "),o("button",{type:"button",onClick:e[10]||(e[10]=a=>p("follow_up","Follow up on quotation","medium")),class:"px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"}," \u{1F4B0} Quote Follow-up "),o("button",{type:"button",onClick:e[11]||(e[11]=a=>p("meeting","Schedule meeting","high")),class:"px-3 py-2 text-xs bg-purple-100 text-purple-800 rounded-md hover:bg-purple-200"}," \u{1F91D} Schedule Meeting "),o("button",{type:"button",onClick:e[12]||(e[12]=a=>p("reminder","Follow up reminder","low")),class:"px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200"}," \u23F0 Set Reminder ")])]),o("div",Y,[o("div",ee,[s(q,{href:i.route("tasks.index")},{svg:u(()=>e[16]||(e[16]=[o("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"}," Cancel ",-1)])),_:1},8,["href"]),s(B,{disabled:l(t).processing},{default:u(()=>e[17]||(e[17]=[_("Save")])),_:1,__:[17]},8,["disabled"]),s(D,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:u(()=>[l(t).recentlySuccessful?(v(),f("p",te,"Saved.")):N("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{me as default};
