import{j as c,T as y,a as w,e as o,b as t,w as r,O as _,d as g,y as i,u as a,M as x,g as v}from"./app.0e820f21.js";import{D as m}from"./DangerButton.7efeecc5.js";import{_ as k,a as D}from"./TextInput.a134c4d6.js";import{_ as C}from"./InputLabel.c491b164.js";import{M as b}from"./Modal.c671de5e.js";import{_ as V}from"./SecondaryButton.f2b207b7.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const B={class:"space-y-6"},U={class:"p-6"},A={class:"mt-6"},M={class:"mt-6 flex justify-end"},K={__name:"DeleteUserForm",setup($){const l=c(!1),n=c(null),s=y({password:""}),p=()=>{l.value=!0,_(()=>n.value.focus())},u=()=>{s.delete(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>d(),onError:()=>n.value.focus(),onFinish:()=>s.reset()})},d=()=>{l.value=!1,s.reset()};return(N,e)=>(g(),w("section",B,[e[6]||(e[6]=o("header",null,[o("h2",{class:"text-lg font-medium text-gray-900"},"Delete Account"),o("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ")],-1)),t(m,{onClick:p},{default:r(()=>e[1]||(e[1]=[i("Delete Account")])),_:1,__:[1]}),t(b,{show:l.value,onClose:d},{default:r(()=>[o("div",U,[e[4]||(e[4]=o("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete your account? ",-1)),e[5]||(e[5]=o("p",{class:"text-sm text-gray-500"}," Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. ",-1)),o("div",A,[t(C,{for:"password",value:"Password",class:"sr-only"}),t(k,{id:"password",ref_key:"passwordInput",ref:n,modelValue:a(s).password,"onUpdate:modelValue":e[0]||(e[0]=f=>a(s).password=f),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:x(u,["enter"])},null,8,["modelValue"]),t(D,{message:a(s).errors.password,class:"mt-2"},null,8,["message"])]),o("div",M,[t(V,{onClick:d},{default:r(()=>e[2]||(e[2]=[i(" Cancel ")])),_:1,__:[2]}),t(m,{class:v(["ml-3",{"opacity-25":a(s).processing}]),disabled:a(s).processing,onClick:u},{default:r(()=>e[3]||(e[3]=[i(" Delete Account ")])),_:1,__:[3]},8,["class","disabled"])])])]),_:1},8,["show"])]))}};export{K as default};
