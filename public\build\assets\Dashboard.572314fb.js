import{_ as p,a as u}from"./AdminLayout.687face1.js";import{c as y,a as l,b as a,u as b,w as c,F as m,d as i,Z as w,e,y as x,t as o,r as f,f as h}from"./app.0e820f21.js";import"./plugin-vue_export-helper.21dcd24c.js";const z={class:"animate-top"},k={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},M={class:"flex space-x-3"},V={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8"},j={class:"bg-white overflow-hidden shadow rounded-lg"},B={class:"p-5"},C={class:"flex items-center"},H={class:"ml-5 w-0 flex-1"},N={class:"text-lg font-medium text-gray-900"},D={class:"bg-white overflow-hidden shadow rounded-lg"},O={class:"p-5"},S={class:"flex items-center"},L={class:"ml-5 w-0 flex-1"},P={class:"text-lg font-medium text-gray-900"},F={class:"bg-white overflow-hidden shadow rounded-lg"},q={class:"p-5"},A={class:"flex items-center"},E={class:"ml-5 w-0 flex-1"},G={class:"text-lg font-medium text-gray-900"},I={class:"bg-white overflow-hidden shadow rounded-lg"},U={class:"p-5"},$={class:"flex items-center"},T={class:"ml-5 w-0 flex-1"},Z={class:"text-lg font-medium text-gray-900"},J={class:"bg-white overflow-hidden shadow rounded-lg"},K={class:"p-5"},Q={class:"flex items-center"},R={class:"ml-5 w-0 flex-1"},W={class:"text-lg font-medium text-gray-900"},X={class:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"},Y={class:"bg-white shadow rounded-lg"},ee={class:"px-4 py-5 sm:p-6"},te={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},se={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},oe={class:"space-y-3 max-h-96 overflow-y-auto"},de={class:"flex items-center justify-between"},le={class:"text-sm font-medium text-blue-600"},ie={class:"text-sm text-gray-600"},ne={class:"text-xs text-gray-500"},re={class:"text-right"},ae={class:"text-sm font-semibold text-green-600"},ce={key:0,class:"text-center py-4 text-gray-500 text-sm"},ue={class:"bg-white shadow rounded-lg"},me={class:"px-4 py-5 sm:p-6"},xe={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},he={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"},ve={class:"space-y-3 max-h-96 overflow-y-auto"},ge={class:"flex items-center justify-between"},fe={class:"text-sm font-medium text-blue-600"},_e={class:"text-sm text-gray-600"},pe={class:"text-xs text-gray-500"},ye={class:"text-right"},be={class:"text-sm font-semibold text-green-600"},we={key:0,class:"text-center py-4 text-gray-500 text-sm"},ze={class:"bg-white shadow rounded-lg"},ke={class:"px-4 py-5 sm:p-6"},Me={class:"text-lg leading-6 font-medium text-gray-900 mb-4"},Ve={class:"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"},je={class:"space-y-3 max-h-96 overflow-y-auto"},Be={class:"flex items-center justify-between"},Ce={class:"text-sm font-medium text-blue-600"},He={class:"text-sm text-gray-600"},Ne={key:0,class:"text-xs text-blue-600 font-mono"},De={class:"text-xs text-gray-500"},Oe={class:"text-right"},Se={class:"text-sm font-semibold text-green-600"},Le={key:0,class:"text-center py-4 text-gray-500 text-sm"},Ae={__name:"Dashboard",props:{orders:{type:Object,required:!0},stats:{type:Object,required:!0}},setup(r){const _=r,v=d=>new Intl.NumberFormat("en-GB",{style:"currency",currency:"GBP"}).format(d),g=d=>d?new Date(d).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"}):"N/A",n=y(()=>{const d={pending:[],confirmed:[],under_production:[],shipped:[],delivered:[]};return _.orders.data.forEach(t=>{d[t.status]&&d[t.status].push(t)}),d});return(d,t)=>(i(),l(m,null,[a(b(w),{title:"Orders Dashboard"}),a(p,null,{default:c(()=>[e("div",z,[e("div",k,[t[1]||(t[1]=e("div",null,[e("h1",{class:"text-3xl font-bold text-gray-900"},"Orders Dashboard"),e("p",{class:"text-gray-600 mt-1"},"Overview of all orders and their statuses")],-1)),e("div",M,[a(u,{href:d.route("orders.index")},{svg:c(()=>t[0]||(t[0]=[e("button",{class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 17.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"})]),x(" View All Orders ")],-1)])),_:1},8,["href"])])]),e("div",V,[e("div",j,[e("div",B,[e("div",C,[t[3]||(t[3]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])])],-1)),e("div",H,[e("dl",null,[t[2]||(t[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Pending",-1)),e("dd",N,o(r.stats.pending||0),1)])])])])]),e("div",D,[e("div",O,[e("div",S,[t[5]||(t[5]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z","clip-rule":"evenodd"})])])],-1)),e("div",L,[e("dl",null,[t[4]||(t[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Confirmed",-1)),e("dd",P,o(r.stats.confirmed||0),1)])])])])]),e("div",F,[e("div",q,[e("div",A,[t[7]||(t[7]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z","clip-rule":"evenodd"})])])],-1)),e("div",E,[e("dl",null,[t[6]||(t[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"In Production",-1)),e("dd",G,o(r.stats.under_production||0),1)])])])])]),e("div",I,[e("div",U,[e("div",$,[t[9]||(t[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-indigo-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM15 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"}),e("path",{d:"M3 4a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1V5a1 1 0 00-1-1H3zM3 10a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM3 16a1 1 0 00-1 1v1a1 1 0 001 1h1a1 1 0 001-1v-1a1 1 0 00-1-1H3zM7 4a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1V5a1 1 0 00-1-1H7zM7 10a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7zM7 16a1 1 0 00-1 1v1a1 1 0 001 1h10a1 1 0 001-1v-1a1 1 0 00-1-1H7z"})])])],-1)),e("div",T,[e("dl",null,[t[8]||(t[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Shipped",-1)),e("dd",Z,o(r.stats.shipped||0),1)])])])])]),e("div",J,[e("div",K,[e("div",Q,[t[11]||(t[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])])],-1)),e("div",R,[e("dl",null,[t[10]||(t[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Delivered",-1)),e("dd",W,o(r.stats.delivered||0),1)])])])])])]),e("div",X,[e("div",Y,[e("div",ee,[e("h3",te,[t[12]||(t[12]=x(" Confirmed Orders ")),e("span",se,o(n.value.confirmed.length),1)]),e("div",oe,[(i(!0),l(m,null,f(n.value.confirmed,s=>(i(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",de,[e("div",null,[e("p",le,o(s.order_number),1),e("p",ie,o(s.client_name),1),e("p",ne,o(g(s.expected_delivery)),1)]),e("div",re,[e("p",ae,o(v(s.total_amount)),1),a(u,{href:d.route("orders.show",s.id)},{svg:c(()=>t[13]||(t[13]=[e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1)])),_:2},1032,["href"])])])]))),128)),n.value.confirmed.length===0?(i(),l("div",ce," No confirmed orders ")):h("",!0)])])]),e("div",ue,[e("div",me,[e("h3",xe,[t[14]||(t[14]=x(" Under Production ")),e("span",he,o(n.value.under_production.length),1)]),e("div",ve,[(i(!0),l(m,null,f(n.value.under_production,s=>(i(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",ge,[e("div",null,[e("p",fe,o(s.order_number),1),e("p",_e,o(s.client_name),1),e("p",pe,o(g(s.expected_delivery)),1)]),e("div",ye,[e("p",be,o(v(s.total_amount)),1),a(u,{href:d.route("orders.show",s.id)},{svg:c(()=>t[15]||(t[15]=[e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1)])),_:2},1032,["href"])])])]))),128)),n.value.under_production.length===0?(i(),l("div",we," No orders in production ")):h("",!0)])])]),e("div",ze,[e("div",ke,[e("h3",Me,[t[16]||(t[16]=x(" Shipped Orders ")),e("span",Ve,o(n.value.shipped.length),1)]),e("div",je,[(i(!0),l(m,null,f(n.value.shipped,s=>(i(),l("div",{key:s.id,class:"border border-gray-200 rounded-lg p-3 hover:bg-gray-50"},[e("div",Be,[e("div",null,[e("p",Ce,o(s.order_number),1),e("p",He,o(s.client_name),1),s.tracking_number?(i(),l("p",Ne,o(s.tracking_number),1)):h("",!0),e("p",De,o(g(s.expected_delivery)),1)]),e("div",Oe,[e("p",Se,o(v(s.total_amount)),1),a(u,{href:d.route("orders.show",s.id)},{svg:c(()=>t[17]||(t[17]=[e("button",{class:"text-xs text-blue-600 hover:text-blue-800"},"View",-1)])),_:2},1032,["href"])])])]))),128)),n.value.shipped.length===0?(i(),l("div",Le," No shipped orders ")):h("",!0)])])])])])]),_:1})],64))}};export{Ae as default};
