import{_ as U,a as A}from"./AdminLayout.687face1.js";import{a as d,b as w,u as O,w as h,F as B,d as l,Z as j,e as t,t as o,f as a,g as I,y as S}from"./app.0e820f21.js";import"./plugin-vue_export-helper.21dcd24c.js";const P={class:"animate-top"},T={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},z={class:"text-3xl font-bold text-gray-900"},K={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},M={class:"lg:col-span-2 space-y-8"},Q={class:"bg-white shadow rounded-lg p-6"},V={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$={class:"mt-1 text-sm text-gray-700"},E={class:"mt-1 text-sm text-gray-700"},F={class:"mt-1 text-sm text-gray-700"},G={class:"mt-1 text-sm text-gray-700"},Z={class:"bg-white shadow rounded-lg p-6"},H={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},J={class:"mt-1 text-sm text-gray-700"},R={class:"mt-1 text-sm text-gray-700"},W={class:"mt-1 text-sm text-gray-700"},X={class:"mt-1 text-sm text-gray-700"},Y={class:"mt-1 text-sm text-gray-700"},tt={class:"mt-1 text-sm text-gray-700"},et={key:0,class:"md:col-span-2"},st={class:"mt-1 text-sm text-gray-700"},ot={class:"bg-white shadow rounded-lg p-6"},rt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},dt={key:0},lt={class:"mt-1 text-sm text-gray-700"},at={class:"mt-1 text-sm text-gray-700"},nt={key:1},it={class:"mt-1 text-sm text-gray-700"},ct={class:"mt-1 text-sm text-gray-700"},mt={key:2},xt={class:"mt-1 text-sm text-gray-700"},yt={class:"bg-white shadow rounded-lg p-6"},gt={class:"overflow-x-auto"},ut={class:"min-w-full divide-y divide-gray-200"},bt={class:"bg-white divide-y divide-gray-200"},ft={key:0},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},ht={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},pt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},vt={key:1},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},qt={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Nt={key:2},At={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},St={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ct={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Lt={key:3},Dt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ot={class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-700"},Bt={class:"bg-gray-50"},jt={class:"px-6 py-4 text-sm font-bold text-green-600"},It={key:0,class:"bg-white shadow rounded-lg p-6"},Pt={class:"text-sm text-gray-700 whitespace-pre-wrap"},Tt={class:"space-y-6"},zt={class:"bg-white shadow rounded-lg p-6"},Kt={class:"space-y-3 w-full"},Mt={class:"bg-white shadow rounded-lg p-6"},Qt={class:"space-y-4"},Vt={class:"flex items-start space-x-3"},$t={class:"text-xs text-gray-500"},Et={key:0,class:"flex items-start space-x-3"},Ft={class:"text-xs text-gray-500"},Gt={key:1,class:"flex items-start space-x-3"},Zt={class:"text-xs text-gray-500"},Wt={__name:"Show",props:{order:{type:Object,required:!0}},setup(s){const C=s,L=r=>({pending:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-indigo-100 text-indigo-800",delivered:"bg-green-100 text-green-800"})[r]||"bg-gray-100 text-gray-800",i=r=>r?new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",n=r=>{var m,x;const e={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},y=((x=(m=C.order.lead)==null?void 0:m.county)==null?void 0:x.name)||"UK",g=Object.keys(e).find(f=>y.toLowerCase().includes(f.toLowerCase())),{locale:u,currency:c}=e[g]||e.UK,b=new Intl.NumberFormat(u,{style:"currency",currency:c,currencyDisplay:"symbol"}).format(r);return`${c} ${b}`},D=r=>({pending:"Pending",confirmed:"Confirmed",under_production:"Under Production",shipped:"Shipped",delivered:"Delivered"})[r]||r;return(r,e)=>(l(),d(B,null,[w(O(j),{title:"Orders"}),w(U,null,{default:h(()=>{var y,g,u,c,b,m,x,f,p,v,k,_,q,N;return[t("div",P,[t("div",T,[t("div",null,[t("h1",z,o(s.order.order_number),1),e[0]||(e[0]=t("p",{class:"text-gray-600 mt-1"},"Order Details",-1))])]),t("div",K,[t("div",M,[t("div",Q,[e[5]||(e[5]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Lead Information",-1)),t("div",V,[t("div",null,[e[1]||(e[1]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1)),t("p",$,o(((y=s.order.lead)==null?void 0:y.client_name)||"N/A"),1)]),t("div",null,[e[2]||(e[2]=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1)),t("p",E,o(((u=(g=s.order.lead)==null?void 0:g.county)==null?void 0:u.name)||"N/A"),1)]),t("div",null,[e[3]||(e[3]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Number",-1)),t("p",F,o(((c=s.order.lead)==null?void 0:c.lead_number)||"N/A"),1)]),t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lead Status",-1)),t("p",G,o(((b=s.order.lead)==null?void 0:b.status)||"N/A"),1)])])]),t("div",Z,[e[13]||(e[13]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1)),t("div",H,[t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1)),t("p",J,o(((m=s.order.lead)==null?void 0:m.dimensions)||"N/A"),1)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1)),t("p",R,o(((x=s.order.lead)==null?void 0:x.open_size)||"N/A"),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1)),t("p",W,o(((f=s.order.lead)==null?void 0:f.box_style)||"N/A"),1)]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1)),t("p",X,o(((p=s.order.lead)==null?void 0:p.stock)||"N/A"),1)]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1)),t("p",Y,o(((v=s.order.lead)==null?void 0:v.lamination)||"N/A"),1)]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1)),t("p",tt,o(((k=s.order.lead)==null?void 0:k.printing)||"N/A"),1)]),(_=s.order.lead)!=null&&_.add_ons?(l(),d("div",et,[e[12]||(e[12]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1)),t("p",st,o(s.order.lead.add_ons),1)])):a("",!0)])]),t("div",ot,[e[20]||(e[20]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Information",-1)),t("div",rt,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1)),t("span",{class:I(["inline-flex px-3 py-1 rounded-full text-sm font-semibold mt-1",L(s.order.status)])},o(D(s.order.status)),3)]),s.order.quotation?(l(),d("div",dt,[e[15]||(e[15]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Quotation Number",-1)),t("p",lt,o(s.order.quotation.quotation_number),1)])):a("",!0),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1)),t("p",at,o((q=s.order.creator)==null?void 0:q.first_name)+" "+o((N=s.order.creator)==null?void 0:N.last_name),1)]),s.order.tracking_number?(l(),d("div",nt,[e[17]||(e[17]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Tracking Number",-1)),t("p",it,o(s.order.tracking_number),1)])):a("",!0),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Expected Delivery",-1)),t("p",ct,o(i(s.order.expected_delivery)),1)]),s.order.actual_delivery?(l(),d("div",mt,[e[19]||(e[19]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Actual Delivery",-1)),t("p",xt,o(i(s.order.actual_delivery)),1)])):a("",!0)])]),t("div",yt,[e[23]||(e[23]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Order Quantities",-1)),t("div",gt,[t("table",ut,[e[22]||(e[22]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Unit Price"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Total")])],-1)),t("tbody",bt,[s.order.selected_qty_1&&s.order.price_qty_1?(l(),d("tr",ft,[t("td",wt,o(parseInt(s.order.selected_qty_1).toLocaleString())+" pcs",1),t("td",ht,o(n(s.order.price_qty_1)),1),t("td",pt,o(n(s.order.selected_qty_1*s.order.price_qty_1)),1)])):a("",!0),s.order.selected_qty_2&&s.order.price_qty_2?(l(),d("tr",vt,[t("td",kt,o(parseInt(s.order.selected_qty_2).toLocaleString())+" pcs",1),t("td",_t,o(n(s.order.price_qty_2)),1),t("td",qt,o(n(s.order.selected_qty_2*s.order.price_qty_2)),1)])):a("",!0),s.order.selected_qty_3&&s.order.price_qty_3?(l(),d("tr",Nt,[t("td",At,o(parseInt(s.order.selected_qty_3).toLocaleString())+" pcs",1),t("td",St,o(n(s.order.price_qty_3)),1),t("td",Ct,o(n(s.order.selected_qty_3*s.order.price_qty_3)),1)])):a("",!0),s.order.selected_qty_4&&s.order.price_qty_4?(l(),d("tr",Lt,[t("td",Dt,o(parseInt(s.order.selected_qty_4).toLocaleString())+" pcs",1),t("td",Ut,o(n(s.order.price_qty_4)),1),t("td",Ot,o(n(s.order.selected_qty_4*s.order.price_qty_4)),1)])):a("",!0)]),t("tfoot",Bt,[t("tr",null,[e[21]||(e[21]=t("td",{colspan:"2",class:"px-6 py-4 text-sm font-semibold text-gray-900 text-right"},"Total Amount:",-1)),t("td",jt,o(n(s.order.total_amount)),1)])])])])]),s.order.notes?(l(),d("div",It,[e[24]||(e[24]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1)),t("p",Pt,o(s.order.notes),1)])):a("",!0)]),t("div",Tt,[t("div",zt,[e[27]||(e[27]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1)),t("div",Kt,[w(A,{href:r.route("orders.edit",s.order.id),class:"w-full"},{svg:h(()=>e[25]||(e[25]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),S(" Edit Order ")],-1)])),_:1},8,["href"]),w(A,{href:r.route("orders.index"),class:"w-full"},{svg:h(()=>e[26]||(e[26]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),S(" Back to Orders ")],-1)])),_:1},8,["href"])])]),t("div",Mt,[e[34]||(e[34]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1)),t("div",Qt,[t("div",Vt,[e[29]||(e[29]=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1)),t("div",null,[e[28]||(e[28]=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Created",-1)),t("p",$t,o(i(s.order.created_at)),1)])]),s.order.is_confirmed?(l(),d("div",Et,[e[31]||(e[31]=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1)),t("div",null,[e[30]||(e[30]=t("p",{class:"text-sm font-semibold text-gray-900"},"Order Confirmed",-1)),t("p",Ft,o(i(s.order.confirmed_at)),1)])])):a("",!0),s.order.updated_at!==s.order.created_at?(l(),d("div",Gt,[e[33]||(e[33]=t("div",{class:"flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"},null,-1)),t("div",null,[e[32]||(e[32]=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1)),t("p",Zt,o(i(s.order.updated_at)),1)])])):a("",!0)])])])])])]}),_:1})],64))}};export{Wt as default};
