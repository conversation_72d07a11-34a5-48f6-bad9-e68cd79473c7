import{j as _,c as U,a as n,b as o,u as h,w as g,F as M,d as a,Z as me,e,l as B,B as pe,K as ge,f as A,r as E,p as T,y as j,t as i,v as ve,E as fe,g as F,N as I}from"./app.0e820f21.js";import{_ as ye,b as _e,a as R}from"./AdminLayout.687face1.js";import{_ as xe}from"./SecondaryButton.f2b207b7.js";import{D as be}from"./DangerButton.7efeecc5.js";import{M as he}from"./Modal.c671de5e.js";import{s as we,_ as ke}from"./sortAndSearch.29e714e8.js";import{_ as N}from"./SearchableDropdownNew.0cffdca8.js";import{_ as O}from"./InputLabel.c491b164.js";import{_ as Ce}from"./ArrowIcon.f2c2b1ba.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const Ae={class:"animate-top"},Se={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},Oe={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},Ve={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},De={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Ue={class:"flex justify-between mb-2"},Me={class:"flex"},Ne={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Pe={key:0,class:"sm:col-span-3"},Le={class:"relative mt-2"},$e={class:"sm:col-span-3"},Be={class:"relative mt-2"},Ee={class:"sm:col-span-3"},Te={class:"relative mt-2"},je={class:"sm:col-span-3"},ze={class:"relative mt-2"},Fe={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Ie={class:"shadow rounded-lg"},Re={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Ge={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Ke={class:"border-b-2"},qe=["onClick"],We={key:0},Qe={class:"px-4 py-2.5 min-w-36"},Ye={class:"px-4 py-2.5 min-w-36"},He={class:"px-4 py-2.5 min-w-28"},Xe={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},Ze={class:"px-4 py-2.5 min-w-36"},Je={class:"px-4 py-2.5 font-semibold text-green-600 min-w-36"},et={class:"px-4 py-2.5"},tt={key:0,class:"text-gray-700 text-sm"},st={key:1,class:"text-gray-400 text-sm"},lt={class:"px-4 py-2.5 min-w-36"},at={class:"px-4 py-2.5 min-w-44"},ot={key:0,class:"flex items-center space-x-2"},nt=["onUpdate:modelValue","onChange"],it=["value"],rt=["onClick"],dt={key:1,class:"flex items-center space-x-2"},ut=["onClick"],ct={class:"px-4 py-2.5 min-w-40"},mt={class:"flex items-center space-x-2 min-w-40"},pt={key:0,class:"px-4 py-2.5"},gt={class:"items-center px-4 py-2.5"},vt={class:"flex items-center justify-start gap-4"},ft=["onClick"],yt=["onClick"],_t={key:1},xt={class:"p-6"},bt={class:"mt-6 flex justify-end"},Pt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","productionStages","production_stage","isAdmin"],setup(v){const r=v,{form:P,search:V,sort:G,fetchData:ht,sortKey:K,sortDirection:q}=we("orders.index"),L=_(!1),z=_(null),W=[{id:"confirmed",name:"Confirmed"},{id:"under_production",name:"Under Production"},{id:"shipped",name:"Shipped"},{id:"delivered",name:"Delivered"}],Q=U(()=>[{id:"",name:"All Agents"},...r.agents]),Y=U(()=>[{id:"",name:"All Country"},...r.counties]),H=U(()=>[{id:"",name:"All Stages"},...r.productionStages]),X=U(()=>[{id:"",name:"All Status"},...r.statusOptions]),Z=[{field:"order_number",label:"ORDER NO",sortable:!0,visible:!0},{field:"quotation.quotation_number",label:"QUOTATION NO",sortable:!1,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!1,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"lead.county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"total_amount",label:"TOTAL AMOUNT",sortable:!0,visible:!0},{field:"tracking_number",label:"TRACKING",sortable:!1,visible:!0},{field:"expected_delivery",label:"EXP. DELIVERY",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"production_stage",label:"PRODUCTION STAGE",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:r.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],J=l=>{z.value=l,L.value=!0},$=()=>{L.value=!1},ee=()=>{P.delete(route("orders.destroy",{order:z.value}),{onSuccess:()=>$()})},te=l=>({confirmed:"bg-blue-100 text-blue-800",under_production:"bg-purple-100 text-purple-800",shipped:"bg-yellow-100 text-yellow-800",delivered:"bg-green-100 text-green-800"})[l]||"bg-gray-100 text-gray-800",se=l=>({"Pre-press":"bg-blue-100 text-blue-800",Printing:"bg-purple-100 text-purple-800",Lamination:"bg-yellow-100 text-yellow-800","Die cutting":"bg-green-100 text-green-800","Quality assurance":"bg-red-100 text-red-800"})[l]||"bg-gray-100 text-gray-800",u=_(r.agent_id||""),c=_(r.county_id||""),m=_(r.status||""),f=_(r.production_stage||""),w=_(""),x=_({}),le=(l,s)=>{u.value=l,S(w.value,u.value,c.value,m.value,f.value)},ae=(l,s)=>{c.value=l,S(w.value,u.value,c.value,m.value,f.value)},oe=(l,s)=>{m.value=l,S(w.value,u.value,c.value,m.value,f.value)},ne=(l,s)=>{f.value=l,S(w.value,u.value,c.value,m.value,f.value)},S=(l,s,t,d,y)=>{w.value=l;const b=s===""?null:s,k=t===""?null:t,C=d===""?null:d,D=y===""?null:y;P.get(route("orders.index",{search:l,agent_id:b,county_id:k,status:C,production_stage:D}),{preserveState:!0})},ie=(l,s)=>{x.value[l]=s},re=l=>{delete x.value[l]},de=l=>{window.open(route("orders.pdf",l),"_blank")},ue=(l,s)=>{I.post(route("orders.update-status",l),{status:s},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete x.value[l];const d=new URLSearchParams(window.location.search).get("page")||1;I.get(route("orders.index"),{search:w.value,agent_id:u.value===""?null:u.value,county_id:c.value===""?null:c.value,status:m.value===""?null:m.value,page:d},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},ce=(l,s)=>{const t={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},d=Object.keys(t).find(C=>s==null?void 0:s.toLowerCase().includes(C.toLowerCase())),{locale:y,currency:b}=t[d]||t["United Kingdom"],k=new Intl.NumberFormat(y,{style:"currency",currency:b,currencyDisplay:"symbol"}).format(l);return`${b} ${k}`};return(l,s)=>(a(),n(M,null,[o(h(me),{title:"Orders"}),o(ye,null,{default:g(()=>[e("div",Ae,[e("div",Se,[s[7]||(s[7]=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Orders")],-1)),e("div",Oe,[e("div",Ve,[s[6]||(s[6]=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),B(e("input",{type:"text","onUpdate:modelValue":s[0]||(s[0]=t=>ge(V)?V.value=t:null),onInput:s[1]||(s[1]=t=>S(h(V),u.value,c.value,m.value,f.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for orders..."},null,544),[[pe,h(V)]])])])]),e("div",De,[e("div",Ue,[e("div",Me,[s[8]||(s[8]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),o(O,{for:"filters",value:"Filters"})])]),e("div",Ne,[r.isAdmin?(a(),n("div",Pe,[o(O,{for:"agent_filter",value:"Agents"}),e("div",Le,[o(N,{options:Q.value,modelValue:u.value,"onUpdate:modelValue":s[2]||(s[2]=t=>u.value=t),onOnchange:le},null,8,["options","modelValue"])])])):A("",!0),e("div",$e,[o(O,{for:"county_filter",value:"Country"}),e("div",Be,[o(N,{options:Y.value,modelValue:c.value,"onUpdate:modelValue":s[3]||(s[3]=t=>c.value=t),onOnchange:ae},null,8,["options","modelValue"])])]),e("div",Ee,[o(O,{for:"status_filter",value:"Status"}),e("div",Te,[o(N,{options:X.value,modelValue:m.value,"onUpdate:modelValue":s[4]||(s[4]=t=>m.value=t),onOnchange:oe},null,8,["options","modelValue"])])]),e("div",je,[o(O,{for:"production_stage_filter",value:"Production Stage"}),e("div",ze,[o(N,{options:H.value,modelValue:f.value,"onUpdate:modelValue":s[5]||(s[5]=t=>f.value=t),onOnchange:ne},null,8,["options","modelValue"])])])])]),e("div",Fe,[e("div",Ie,[e("table",Re,[e("thead",Ge,[e("tr",Ke,[(a(),n(M,null,E(Z,(t,d)=>B(e("th",{key:d,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:y=>h(G)(t.field,t.sortable)},[j(i(t.label)+" ",1),t.sortable?(a(),T(Ce,{key:0,isSorted:h(K)===t.field,direction:h(q)},null,8,["isSorted","direction"])):A("",!0)],8,qe),[[ve,t.visible]])),64))])]),v.data.data&&v.data.data.length>0?(a(),n("tbody",We,[(a(!0),n(M,null,E(v.data.data,t=>{var d,y,b,k,C,D;return a(),n("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",Qe,i(t.order_number),1),e("td",Ye,i(t.quotation?t.quotation.quotation_number:"N/A"),1),e("td",He,i(t.lead?t.lead.lead_number:"N/A"),1),e("td",Xe,i((d=t.lead)==null?void 0:d.client_name),1),e("td",Ze,i((b=(y=t.lead)==null?void 0:y.county)==null?void 0:b.name),1),e("td",Je,i(ce(t.total_amount,(C=(k=t.lead)==null?void 0:k.county)==null?void 0:C.name)),1),e("td",et,[t.tracking_number?(a(),n("span",tt,i(t.tracking_number),1)):(a(),n("span",st,"-"))]),e("td",lt,i(t.expected_delivery?new Date(t.expected_delivery).toLocaleDateString("en-GB"):"N/A"),1),e("td",at,[x.value[t.id]!==void 0?(a(),n("div",ot,[B(e("select",{"onUpdate:modelValue":p=>x.value[t.id]=p,class:"text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500",onChange:p=>ue(t.id,x.value[t.id])},[(a(),n(M,null,E(W,p=>e("option",{key:p.id,value:p.id},i(p.name),9,it)),64))],40,nt),[[fe,x.value[t.id]]]),e("button",{onClick:p=>re(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," \u2715 ",8,rt)])):(a(),n("div",dt,[e("span",{class:F(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",te(t.status)]),onClick:p=>ie(t.id,t.status),title:"Click to edit status"},i(t.status.charAt(0).toUpperCase()+t.status.slice(1).replace("_"," ")),11,ut)]))]),e("td",ct,[e("div",mt,[e("span",{class:F(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",se(t.production_stage)])},i((D=t.production_stage)!=null?D:"N/A"),3)])]),r.isAdmin?(a(),n("td",pt,i(t.lead.creator?t.lead.creator.first_name:"N/A"),1)):A("",!0),e("td",gt,[e("div",vt,[o(_e,{align:"right",width:"48"},{trigger:g(()=>s[9]||(s[9]=[e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)])),content:g(()=>[o(R,{href:l.route("orders.show",{order:t.id})},{svg:g(()=>s[10]||(s[10]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1)])),text:g(()=>s[11]||(s[11]=[e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1)])),_:2},1032,["href"]),v.permissions.canEditOrder?(a(),T(R,{key:0,href:l.route("orders.edit",{order:t.id})},{svg:g(()=>s[12]||(s[12]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)])),text:g(()=>s[13]||(s[13]=[e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1)])),_:2},1032,["href"])):A("",!0),v.permissions.canDeleteOrder?(a(),n("button",{key:1,type:"button",onClick:p=>J(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},s[14]||(s[14]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1)]),8,ft)):A("",!0),e("button",{type:"button",onClick:p=>de(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},s[15]||(s[15]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1)]),8,yt)]),_:2},1024)])])])}),128))])):(a(),n("tbody",_t,s[16]||(s[16]=[e("tr",{class:"bg-white"},[e("td",{colspan:"12",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)])))])])]),v.data.data&&v.data.data.length>0?(a(),T(ke,{key:0,class:"mt-6",links:v.data.links},null,8,["links"])):A("",!0)]),o(he,{show:L.value,onClose:$},{default:g(()=>[e("div",xt,[s[19]||(s[19]=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this order? ",-1)),e("div",bt,[o(xe,{onClick:$},{default:g(()=>s[17]||(s[17]=[j("Cancel")])),_:1,__:[17]}),o(be,{class:"ml-3",onClick:ee,disabled:h(P).processing},{default:g(()=>s[18]||(s[18]=[j(" Delete Order ")])),_:1,__:[18]},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Pt as default};
