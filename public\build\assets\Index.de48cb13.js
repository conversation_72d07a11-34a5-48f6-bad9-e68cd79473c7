import{j as A,a as n,b as f,u as m,w as x,F as v,d as r,Z as F,e,t as a,l as y,E as _,r as R,f as d,p as h,y as k,q as w,N as u,z as C,g}from"./app.0e820f21.js";import{_ as $}from"./AdminLayout.687face1.js";import"./plugin-vue_export-helper.21dcd24c.js";const D={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},O={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},q={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},H={class:"flex items-center"},L={class:"ml-4"},E={class:"text-2xl font-semibold text-blue-900"},P={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Q={class:"flex items-center"},I={class:"ml-4"},W={class:"text-2xl font-semibold text-yellow-900"},Y={class:"bg-red-50 border border-red-200 rounded-lg p-4"},Z={class:"flex items-center"},G={class:"ml-4"},J={class:"text-2xl font-semibold text-red-900"},K={class:"bg-gray-50 p-4 rounded-lg mb-6"},X={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},ee={class:"space-y-3"},te={class:"flex items-start justify-between"},se={class:"flex-1"},oe={class:"flex items-center space-x-2 mb-2"},re={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"},le={class:"text-sm font-semibold text-gray-900 mb-1"},ae={class:"text-sm text-gray-600 mb-2"},ne={class:"flex items-center space-x-4 text-xs text-gray-500"},de={key:0},ie={class:"flex flex-col space-y-2 ml-4"},ue=["onClick"],ce=["onClick"],me=["onClick"],xe={key:0,class:"text-center py-12"},ge={key:0,class:"mt-6"},be={class:"flex items-center justify-between"},pe={class:"flex-1 flex justify-between sm:hidden"},_e={__name:"Index",props:{notifications:Object,stats:Object},setup(l){const i=A({type:"",unread_only:""}),c=()=>{u.get(route("notifications.index"),i.value,{preserveState:!0,preserveScroll:!0})},N=()=>{i.value={type:"",unread_only:""},c()},b=async s=>{try{await C.post(`/notifications/${s}/read`),u.reload({only:["notifications","stats"]})}catch(t){console.error("Failed to mark notification as read:",t)}},j=async()=>{try{await C.post("/notifications/read-all"),u.reload({only:["notifications","stats"]})}catch(s){console.error("Failed to mark all as read:",s)}},z=s=>{confirm("Delete this notification?")&&u.delete(route("notifications.destroy",s),{onSuccess:()=>{u.reload({only:["notifications","stats"]})}})},M=s=>{s.action_data&&s.action_data.url&&(s.is_read||b(s.id),window.location.href=s.action_data.url)},S=s=>new Date(s).toLocaleString(),T=s=>s.replace("_"," ").replace(/\b\w/g,t=>t.toUpperCase()),U=s=>s.client_name?s.client_name:s.quotation_number?`Quotation ${s.quotation_number}`:s.order_number?`Order ${s.order_number}`:s.title?s.title:"Unknown",V=s=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[s]||"bg-gray-100 text-gray-800",B=s=>({task_reminder:"bg-blue-100 text-blue-800",task_overdue:"bg-red-100 text-red-800",lead_update:"bg-green-100 text-green-800",quotation_update:"bg-purple-100 text-purple-800",order_update:"bg-orange-100 text-orange-800",system:"bg-gray-100 text-gray-800"})[s]||"bg-gray-100 text-gray-800";return(s,t)=>(r(),n(v,null,[f(m(F),{title:"Notifications"}),f($,null,{default:x(()=>[e("div",D,[e("div",{class:"flex justify-between items-center mb-6"},[t[2]||(t[2]=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Notifications"),e("p",{class:"text-sm text-gray-600 mt-1"},"Stay updated with your tasks and activities")],-1)),e("div",{class:"flex space-x-2"},[e("button",{onClick:j,class:"flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"}," \u2705 Mark All Read ")])]),e("div",O,[e("div",q,[e("div",H,[t[4]||(t[4]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"})])],-1)),e("div",L,[t[3]||(t[3]=e("p",{class:"text-sm font-medium text-blue-600"},"Total",-1)),e("p",E,a(l.stats.total),1)])])]),e("div",P,[e("div",Q,[t[6]||(t[6]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",I,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-yellow-600"},"Unread",-1)),e("p",W,a(l.stats.unread),1)])])]),e("div",Y,[e("div",Z,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",G,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-red-600"},"High Priority",-1)),e("p",J,a(l.stats.high_priority),1)])])])]),e("div",K,[e("div",X,[e("div",null,[t[10]||(t[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Type",-1)),y(e("select",{"onUpdate:modelValue":t[0]||(t[0]=o=>i.value.type=o),onChange:c,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[9]||(t[9]=[e("option",{value:""},"All Types",-1),e("option",{value:"task_reminder"},"Task Reminder",-1),e("option",{value:"task_overdue"},"Task Overdue",-1),e("option",{value:"lead_update"},"Lead Update",-1),e("option",{value:"quotation_update"},"Quotation Update",-1),e("option",{value:"order_update"},"Order Update",-1),e("option",{value:"system"},"System",-1)]),544),[[_,i.value.type]])]),e("div",null,[t[12]||(t[12]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Status",-1)),y(e("select",{"onUpdate:modelValue":t[1]||(t[1]=o=>i.value.unread_only=o),onChange:c,class:"w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"},t[11]||(t[11]=[e("option",{value:""},"All Notifications",-1),e("option",{value:"1"},"Unread Only",-1)]),544),[[_,i.value.unread_only]])]),e("div",{class:"flex items-end"},[e("button",{onClick:N,class:"px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md hover:bg-gray-700"}," Clear Filters ")])])]),e("div",ee,[(r(!0),n(v,null,R(l.notifications.data,o=>(r(),n("div",{key:o.id,class:g(["border rounded-lg p-4 hover:shadow-md transition-shadow",{"bg-blue-50 border-blue-200":!o.is_read,"bg-white":o.is_read}])},[e("div",te,[e("div",se,[e("div",oe,[e("span",{class:g(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",V(o.priority)])},a(o.priority),3),e("span",{class:g(["inline-flex px-2 py-1 text-xs rounded-full",B(o.type)])},a(T(o.type)),3),o.is_read?d("",!0):(r(),n("span",re," NEW "))]),e("h3",le,a(o.title),1),e("p",ae,a(o.message),1),e("div",ne,[e("span",null,a(S(o.created_at)),1),o.notifiable?(r(),n("span",de," Related to: "+a(U(o.notifiable)),1)):d("",!0)])]),e("div",ie,[o.is_read?d("",!0):(r(),n("button",{key:0,onClick:p=>b(o.id),class:"px-3 py-1 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"}," Mark Read ",8,ue)),o.action_data&&o.action_data.url?(r(),n("button",{key:1,onClick:p=>M(o),class:"px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"},a(o.action_data.action||"View"),9,ce)):d("",!0),e("button",{onClick:p=>z(o.id),class:"px-3 py-1 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200"}," Delete ",8,me)])])],2))),128)),l.notifications.data.length===0?(r(),n("div",xe,t[13]||(t[13]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2h6a2 2 0 002-2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No notifications",-1),e("p",{class:"mt-1 text-sm text-gray-500"},"You're all caught up!",-1)]))):d("",!0)]),l.notifications.links?(r(),n("div",ge,[e("nav",be,[e("div",pe,[l.notifications.prev_page_url?(r(),h(m(w),{key:0,href:l.notifications.prev_page_url,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},{default:x(()=>t[14]||(t[14]=[k(" Previous ")])),_:1,__:[14]},8,["href"])):d("",!0),l.notifications.next_page_url?(r(),h(m(w),{key:1,href:l.notifications.next_page_url,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},{default:x(()=>t[15]||(t[15]=[k(" Next ")])),_:1,__:[15]},8,["href"])):d("",!0)])])])):d("",!0)])]),_:1})],64))}};export{_e as default};
