import{T as B,j as f,A as D,a as _,b as l,u as d,w as Q,F as j,d as p,Z as L,e as t,t as i,y as N,f as c,x as R}from"./app.0e820f21.js";import{_ as O,a as z}from"./AdminLayout.687face1.js";import{_ as y,a as u}from"./TextInput.a134c4d6.js";import{_ as n}from"./InputLabel.c491b164.js";import{P as M}from"./PrimaryButton.259b896f.js";import{_ as Z}from"./TextArea.3742605b.js";import{_ as V}from"./Checkbox.a38f6303.js";import"./plugin-vue_export-helper.21dcd24c.js";const G={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},H={class:"text-2xl font-semibold leading-7 text-gray-900"},J={key:0,class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},K={class:"text-sm text-blue-800"},W={class:"border-b border-gray-900/10 pb-12"},X={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},h={class:"sm:col-span-12"},ee={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},te={class:"text-sm text-gray-900"},se={class:"text-sm text-gray-900"},le={class:"text-sm text-gray-900"},oe={class:"text-sm text-gray-900"},de={class:"text-sm text-gray-900"},ae={class:"text-sm text-gray-900"},ie={class:"text-sm text-gray-900"},ne={class:"text-sm text-gray-900"},re={key:0},ue={class:"text-sm text-gray-900"},me={class:"sm:col-span-3"},ye={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},_e={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},pe={class:"sm:col-span-3"},ce={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},ge={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},qe={class:"sm:col-span-3"},ve={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},xe={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},fe={class:"sm:col-span-3"},be={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ve={key:0,class:"grid grid-cols-1 sm:grid-cols-2 gap-4"},ke={class:"sm:col-span-12"},Qe={class:"bg-gray-50 p-4 rounded-lg"},Ue={class:"text-lg font-semibold text-gray-900"},Te={class:"sm:col-span-12"},we={class:"flex mt-6 items-center justify-between"},Ce={class:"ml-auto flex items-center justify-end gap-x-6"},Ee={__name:"Add",props:{counties:{type:Array,required:!0},lead:{type:Object,required:!0}},setup(a){var w,C,F,I,S,$,P,Y;const m=a,s=B({lead_id:((w=m.lead)==null?void 0:w.id)||null,qty_1:((C=m.lead)==null?void 0:C.qty_1)||"",qty_2:((F=m.lead)==null?void 0:F.qty_2)||"",qty_3:((I=m.lead)==null?void 0:I.qty_3)||"",qty_4:((S=m.lead)==null?void 0:S.qty_4)||"",price_qty_1:"",price_qty_2:"",price_qty_3:"",price_qty_4:"",notes:"",valid_until:"",documents:[]}),g=f(!0),q=f(!!(($=m.lead)!=null&&$.qty_2)),v=f(!!((P=m.lead)!=null&&P.qty_3)),x=f(!!((Y=m.lead)!=null&&Y.qty_4)),U=f(0),E=()=>{s.post(route("quotations.store"),{preserveScroll:!0,onSuccess:()=>s.reset()})},T=()=>{let r=0;g.value&&s.qty_1&&s.price_qty_1&&(r+=parseFloat(s.qty_1)*parseFloat(s.price_qty_1)),q.value&&s.qty_2&&s.price_qty_2&&(r+=parseFloat(s.qty_2)*parseFloat(s.price_qty_2)),v.value&&s.qty_3&&s.price_qty_3&&(r+=parseFloat(s.qty_3)*parseFloat(s.price_qty_3)),x.value&&s.qty_4&&s.price_qty_4&&(r+=parseFloat(s.qty_4)*parseFloat(s.price_qty_4)),U.value=r},b=(r,e)=>{e||(s[`qty_${r}`]="",s[`price_qty_${r}`]=""),T()};D([()=>s.qty_1,()=>s.price_qty_1,()=>g.value,()=>s.qty_2,()=>s.price_qty_2,()=>q.value,()=>s.qty_3,()=>s.price_qty_3,()=>v.value,()=>s.qty_4,()=>s.price_qty_4,()=>x.value],T);const k=new Date;return k.setDate(k.getDate()+30),s.valid_until=k.toISOString().split("T")[0],(r,e)=>(p(),_(j,null,[l(d(L),{title:"Quotations"}),l(O,null,{default:Q(()=>{var A;return[t("div",G,[t("h2",H,i(a.lead?"Convert Lead to Quotation":"Add New Quotation"),1),a.lead?(p(),_("div",J,[t("p",K,[e[17]||(e[17]=t("strong",null,"Converting from Lead:",-1)),N(" "+i(a.lead.lead_number)+" - "+i(a.lead.client_name),1)])])):c("",!0),t("form",{onSubmit:R(E,["prevent"])},[t("div",W,[t("div",X,[t("div",h,[e[27]||(e[27]=t("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Lead Information",-1)),t("div",ee,[t("div",null,[e[18]||(e[18]=t("p",{class:"text-sm font-medium text-gray-500"},"Client Name",-1)),t("p",te,i(a.lead.client_name),1)]),t("div",null,[e[19]||(e[19]=t("p",{class:"text-sm font-medium text-gray-500"},"County",-1)),t("p",se,i(((A=a.lead.county)==null?void 0:A.name)||"N/A"),1)]),t("div",null,[e[20]||(e[20]=t("p",{class:"text-sm font-medium text-gray-500"},"Dimensions",-1)),t("p",le,i(a.lead.dimensions),1)]),t("div",null,[e[21]||(e[21]=t("p",{class:"text-sm font-medium text-gray-500"},"Open Size",-1)),t("p",oe,i(a.lead.open_size),1)]),t("div",null,[e[22]||(e[22]=t("p",{class:"text-sm font-medium text-gray-500"},"Box Style",-1)),t("p",de,i(a.lead.box_style),1)]),t("div",null,[e[23]||(e[23]=t("p",{class:"text-sm font-medium text-gray-500"},"Stock",-1)),t("p",ae,i(a.lead.stock),1)]),t("div",null,[e[24]||(e[24]=t("p",{class:"text-sm font-medium text-gray-500"},"Lamination",-1)),t("p",ie,i(a.lead.lamination),1)]),t("div",null,[e[25]||(e[25]=t("p",{class:"text-sm font-medium text-gray-500"},"Printing",-1)),t("p",ne,i(a.lead.printing),1)]),a.lead.add_ons?(p(),_("div",re,[e[26]||(e[26]=t("p",{class:"text-sm font-medium text-gray-500"},"Add-ons",-1)),t("p",ue,i(a.lead.add_ons),1)])):c("",!0)])]),e[28]||(e[28]=t("div",{class:"sm:col-span-12 mt-6"},[t("h3",{class:"text-lg font-bold text-gray-900"},"Quantities and Pricing")],-1)),t("div",me,[t("div",ye,[l(V,{checked:g.value,"onUpdate:checked":[e[0]||(e[0]=o=>g.value=o),e[1]||(e[1]=o=>b(1,o))]},null,8,["checked"]),l(n,{value:"Include Quantity 1",class:"text-base font-medium text-blue-800"})]),g.value?(p(),_("div",_e,[t("div",null,[l(n,{for:"qty_1",value:"QTY 1 *"}),l(y,{id:"qty_1",type:"number",modelValue:d(s).qty_1,"onUpdate:modelValue":e[2]||(e[2]=o=>d(s).qty_1=o),min:"1",required:""},null,8,["modelValue"]),l(u,{message:d(s).errors.qty_1},null,8,["message"])]),t("div",null,[l(n,{for:"price_qty_1",value:"PRICE QTY 1 *"}),l(y,{id:"price_qty_1",type:"number",step:"0.01",modelValue:d(s).price_qty_1,"onUpdate:modelValue":e[3]||(e[3]=o=>d(s).price_qty_1=o),min:"0"},null,8,["modelValue"]),l(u,{message:d(s).errors.price_qty_1},null,8,["message"])])])):c("",!0)]),t("div",pe,[t("div",ce,[l(V,{checked:q.value,"onUpdate:checked":[e[4]||(e[4]=o=>q.value=o),e[5]||(e[5]=o=>b(2,o))]},null,8,["checked"]),l(n,{value:"Include Quantity 2",class:"text-base font-medium text-green-800"})]),q.value?(p(),_("div",ge,[t("div",null,[l(n,{for:"qty_2",value:"QTY 2"}),l(y,{id:"qty_2",type:"number",modelValue:d(s).qty_2,"onUpdate:modelValue":e[6]||(e[6]=o=>d(s).qty_2=o),min:"1"},null,8,["modelValue"]),l(u,{message:d(s).errors.qty_2},null,8,["message"])]),t("div",null,[l(n,{for:"price_qty_2",value:"PRICE QTY 2"}),l(y,{id:"price_qty_2",type:"number",step:"0.01",modelValue:d(s).price_qty_2,"onUpdate:modelValue":e[7]||(e[7]=o=>d(s).price_qty_2=o),min:"0"},null,8,["modelValue"]),l(u,{message:d(s).errors.price_qty_2},null,8,["message"])])])):c("",!0)]),t("div",qe,[t("div",ve,[l(V,{checked:v.value,"onUpdate:checked":[e[8]||(e[8]=o=>v.value=o),e[9]||(e[9]=o=>b(3,o))]},null,8,["checked"]),l(n,{value:"Include Quantity 3",class:"text-base font-medium text-yellow-800"})]),v.value?(p(),_("div",xe,[t("div",null,[l(n,{for:"qty_3",value:"QTY 3"}),l(y,{id:"qty_3",type:"number",modelValue:d(s).qty_3,"onUpdate:modelValue":e[10]||(e[10]=o=>d(s).qty_3=o),min:"1"},null,8,["modelValue"]),l(u,{message:d(s).errors.qty_3},null,8,["message"])]),t("div",null,[l(n,{for:"price_qty_3",value:"PRICE QTY 3"}),l(y,{id:"price_qty_3",type:"number",step:"0.01",modelValue:d(s).price_qty_3,"onUpdate:modelValue":e[11]||(e[11]=o=>d(s).price_qty_3=o),min:"0"},null,8,["modelValue"]),l(u,{message:d(s).errors.price_qty_3},null,8,["message"])])])):c("",!0)]),t("div",fe,[t("div",be,[l(V,{checked:x.value,"onUpdate:checked":[e[12]||(e[12]=o=>x.value=o),e[13]||(e[13]=o=>b(4,o))]},null,8,["checked"]),l(n,{value:"Include Quantity 4",class:"text-base font-medium text-purple-800"})]),x.value?(p(),_("div",Ve,[t("div",null,[l(n,{for:"qty_4",value:"QTY 4"}),l(y,{id:"qty_4",type:"number",modelValue:d(s).qty_4,"onUpdate:modelValue":e[14]||(e[14]=o=>d(s).qty_4=o),min:"1"},null,8,["modelValue"]),l(u,{message:d(s).errors.qty_4},null,8,["message"])]),t("div",null,[l(n,{for:"price_qty_4",value:"PRICE QTY 4"}),l(y,{id:"price_qty_4",type:"number",step:"0.01",modelValue:d(s).price_qty_4,"onUpdate:modelValue":e[15]||(e[15]=o=>d(s).price_qty_4=o),min:"0"},null,8,["modelValue"]),l(u,{message:d(s).errors.price_qty_4},null,8,["message"])])])):c("",!0)]),t("div",ke,[t("div",Qe,[t("p",Ue," Estimated Total: $"+i(U.value.toFixed(2)),1)])]),t("div",Te,[l(n,{for:"notes",value:"Notes"}),l(Z,{id:"notes",modelValue:d(s).notes,"onUpdate:modelValue":e[16]||(e[16]=o=>d(s).notes=o),rows:"4"},null,8,["modelValue"]),l(u,{message:d(s).errors.notes},null,8,["message"])])])]),t("div",we,[t("div",Ce,[l(z,{href:r.route("quotations.index")},{svg:Q(()=>e[29]||(e[29]=[t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),l(M,{disabled:d(s).processing},{default:Q(()=>e[30]||(e[30]=[N(" Save ")])),_:1,__:[30]},8,["disabled"])])])],32)])]}),_:1})],64))}};export{Ee as default};
