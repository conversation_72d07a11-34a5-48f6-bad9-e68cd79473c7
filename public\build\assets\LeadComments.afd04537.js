import{j as g,c as B,d as n,a as l,e,t as a,f as x,F as k,r as $,g as _,l as f,B as I,N as y,L as S,y as E,x as F}from"./app.0e820f21.js";import{_ as L}from"./plugin-vue_export-helper.21dcd24c.js";const V={class:"comments-card"},N={key:0,class:"flex items-center justify-between mb-3"},T={class:"flex items-center space-x-2"},D={class:"text-sm font-medium text-gray-900"},R={key:1,class:"space-y-2 overflow-y-auto max-h-12"},q={class:"flex items-center justify-between mb-1"},z={class:"font-medium text-gray-900"},H={class:"text-gray-500"},G={class:"text-gray-600 line-clamp-2"},W={key:2,class:"space-y-3 overflow-x-auto max-h-52"},J={class:"flex items-center justify-between mb-1"},K={class:"flex items-center space-x-2"},O={class:"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center"},Q={class:"text-xs font-medium text-blue-600"},X={class:"font-medium text-gray-900"},Y={class:"flex items-center space-x-2"},Z={class:"text-gray-500"},ee={key:0,class:"flex space-x-1"},te=["onClick"],se=["onClick"],oe={key:0,class:"mt-2"},re=["onUpdate:modelValue"],ae={key:1,class:"flex items-center justify-between"},ne={class:"text-gray-700 whitespace-pre-wrap text-xs"},le={key:0,class:"mt-1 text-xs text-orange-600"},de={key:3,class:"text-center py-4 text-gray-500"},ue={__name:"CommentsCard",props:{comments:{type:Array,default:()=>[]},currentUserId:{type:Number,required:!0},isAdmin:{type:Boolean,default:!1}},emits:["add-comment","comment-updated","comment-deleted"],setup(i,{emit:w}){const b=i,h=w,u=g(!1),d=g(null),v=g({comment:""});B(()=>b.comments.slice(0,2));const c=r=>{const s=new Date,t=new Date(r),m=Math.floor((s-t)/1e3);return m<60?"just now":m<3600?`${Math.floor(m/60)}m ago`:m<86400?`${Math.floor(m/3600)}h ago`:m<604800?`${Math.floor(m/86400)}d ago`:t.toLocaleDateString()},o=r=>({lost_reason:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800"})[r]||"bg-gray-100 text-gray-800",p=r=>({lost_reason:"\u274C",general:"\u{1F4AC}"})[r]||"\u{1F4AC}",M=r=>r.user_id===b.currentUserId||b.isAdmin,A=r=>{d.value=r,v.value.comment=r.comment},P=()=>{d.value=null,v.value.comment=""},U=async()=>{if(!!v.value.comment.trim())try{y.put(route("leads.update-comment",d.value.id),{comment:v.value.comment},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const s=new URLSearchParams(window.location.search).get("page")||1;newComment.value="",commentType.value="general",h("comment-updated",""),y.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:s},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:r=>{console.error("Update failed:",r),alert("Failed to update status. Please try again.")}})}catch(r){console.error("Error updating comment:",r),alert("Failed to update comment")}},j=async r=>{try{y.delete(route("leads.delete-comment",r),{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const t=new URLSearchParams(window.location.search).get("page")||1;newComment.value="",commentType.value="general",h("comment-deleted",""),y.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:t},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:s=>{console.error("Update failed:",s),alert("Failed to update status. Please try again.")}})}catch(s){console.error("Error deleting comment:",s),alert("Failed to delete comment")}};return(r,s)=>(n(),l("div",V,[i.comments.length!=0?(n(),l("div",N,[e("div",T,[s[3]||(s[3]=e("svg",{class:"w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),e("span",D,a(i.comments.length)+" "+a(i.comments.length===1?"Comment":"Comments"),1)]),e("button",{onClick:s[0]||(s[0]=t=>u.value=!u.value),class:"text-xs text-blue-600 font-semibold hover:text-blue-800"},a(u.value?"Hide":"Show"),1)])):x("",!0),!u.value&&i.comments.length>0?(n(),l("div",R,[(n(!0),l(k,null,$(i.comments,t=>(n(),l("div",{key:t.id,class:_(["text-xs p-2 rounded border-l-2",{"border-red-400 bg-red-50":t.type==="lost_reason","border-gray-400 bg-gray-50":t.type==="general"}])},[e("div",q,[e("span",z,a(t.user.first_name)+" "+a(t.user.last_name),1),e("span",H,a(c(t.created_at)),1)]),e("p",G,a(t.comment),1)],2))),128))])):x("",!0),u.value?(n(),l("div",W,[e("button",{onClick:s[1]||(s[1]=t=>r.$emit("add-comment")),class:"w-full py-2 px-3 text-xs text-blue-600 border font-semibold border-blue-200 rounded-md hover:bg-blue-50 transition-colors"}," + Add Comment "),(n(!0),l(k,null,$(i.comments,t=>{var m;return n(),l("div",{key:t.id,class:_(["text-xs py-2 px-3 rounded-lg border",{"border-red-200 bg-red-50":t.type==="lost_reason","border-gray-200 bg-gray-50":t.type==="general"}])},[e("div",J,[e("div",K,[e("div",O,[e("span",Q,a(t.user.first_name.charAt(0)),1)]),e("span",X,a(t.user.first_name)+" "+a(t.user.last_name),1),e("span",{class:_(["inline-flex px-2 py-1 rounded-full text-xs font-medium",o(t.type)])},a(p(t.type)),3)]),e("div",Y,[e("span",Z,a(c(t.created_at)),1),M(t)?(n(),l("div",ee,[e("button",{onClick:C=>A(t),class:"text-blue-600 hover:text-blue-800 text-xs"},s[4]||(s[4]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)]),8,te),e("button",{onClick:C=>j(t.id),class:"text-red-600 hover:text-red-800 text-xs"},s[5]||(s[5]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1)]),8,se)])):x("",!0)])]),((m=d.value)==null?void 0:m.id)===t.id?(n(),l("div",oe,[f(e("textarea",{"onUpdate:modelValue":C=>v.value.comment=C,class:"w-full p-2 border border-gray-300 rounded-md text-xs",rows:"2"},null,8,re),[[I,v.value.comment]]),e("div",{class:"flex space-x-2 mt-2"},[e("button",{onClick:U,class:"px-2 py-1 bg-indigo-600 text-white text-xs rounded-md hover:bg-indigo-700"}," Save "),e("button",{onClick:P,class:"px-2 py-1 bg-gray-300 text-gray-700 text-xs rounded-md hover:bg-gray-400"}," Cancel ")])])):(n(),l("div",ae,[e("p",ne,a(t.comment),1),t.is_edited?(n(),l("div",le," (edited "+a(c(t.edited_at))+") ",1)):x("",!0)]))],2)}),128))])):x("",!0),i.comments.length===0?(n(),l("div",de,[s[6]||(s[6]=e("svg",{class:"mx-auto h-6 w-6 text-gray-400 mb-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})],-1)),s[7]||(s[7]=e("p",{class:"text-xs"},"No comments yet",-1)),e("button",{onClick:s[2]||(s[2]=t=>r.$emit("add-comment")),class:"mt-2 text-xs font-semibold text-blue-600 hover:text-blue-800"}," Add first comment ")])):x("",!0)]))}};var $e=L(ue,[["__scopeId","data-v-6b7be97e"]]);const ie={class:"lead-comments"},ce={key:0,class:"bg-white border border-gray-200 rounded-lg p-4"},me={class:"mb-3"},ve={class:"flex space-x-4"},pe={class:"flex items-center"},xe={class:"flex items-center"},ge=["placeholder"],ye={key:0,class:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md"},be={class:"flex justify-between items-center mt-3"},he={class:"text-xs text-gray-500"},_e={class:"flex space-x-2"},fe=["disabled"],we={__name:"LeadComments",props:{leadId:{type:Number,required:!0},comments:{type:Array,default:()=>[]},showAddForm:{type:Boolean,default:!0},currentUserId:{type:Number,required:!0},isAdmin:{type:Boolean,default:!1}},emits:["commentAdded","commentUpdated","commentDeleted","close"],setup(i,{emit:w}){const b=i,h=w,u=g(""),d=g("general");g(null),g({comment:""});const v=async()=>{if(!!u.value.trim())try{y.post(route("leads.add-comment",b.leadId),{comment:u.value,type:d.value},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{const o=new URLSearchParams(window.location.search).get("page")||1;u.value="",d.value="general",h("close"),h("commentAdded",""),y.get(route("leads.index"),{search:searchValue.value,agent_id:agentId.value===""?null:agentId.value,county_id:countyId.value===""?null:countyId.value,page:o},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:c=>{console.error("Update failed:",c),alert("Failed to update status. Please try again.")}})}catch(c){console.error("Error adding comment:",c),alert("Failed to add comment")}};return(c,o)=>(n(),l("div",ie,[i.showAddForm?(n(),l("div",ce,[o[8]||(o[8]=e("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Add Comment",-1)),e("form",{onSubmit:F(v,["prevent"])},[e("div",me,[o[6]||(o[6]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"Comment Type",-1)),e("div",ve,[e("label",pe,[f(e("input",{type:"radio","onUpdate:modelValue":o[0]||(o[0]=p=>d.value=p),value:"general",class:"mr-2 text-blue-600 focus:ring-blue-500"},null,512),[[S,d.value]]),o[4]||(o[4]=e("span",{class:"text-sm"},"General Comment",-1))]),e("label",xe,[f(e("input",{type:"radio","onUpdate:modelValue":o[1]||(o[1]=p=>d.value=p),value:"lost_reason",class:"mr-2 text-red-600 focus:ring-red-500"},null,512),[[S,d.value]]),o[5]||(o[5]=e("span",{class:"text-sm"},"Lost Reason",-1))])])]),f(e("textarea",{"onUpdate:modelValue":o[2]||(o[2]=p=>u.value=p),placeholder:d.value==="lost_reason"?"Explain why this lead was lost...":"Write your comment here...",class:"w-full p-3 border border-gray-300 rounded-md text-sm",rows:"3",required:""},null,8,ge),[[I,u.value]]),d.value==="lost_reason"?(n(),l("div",ye,o[7]||(o[7]=[e("p",{class:"text-xs text-red-700"},[e("strong",null,"Note:"),E(" Enter the reason why this lead was lost. ")],-1)]))):x("",!0),e("div",be,[e("div",he,a(u.value.length)+"/1000 characters ",1),e("div",_e,[e("button",{type:"button",onClick:o[3]||(o[3]=p=>c.$emit("close")),class:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800"}," Cancel "),e("button",{type:"submit",disabled:!u.value.trim()||u.value.length>1e3,class:_(["px-4 py-2 text-white text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed",d.value==="lost_reason"?"bg-red-600 hover:bg-red-700":"bg-indigo-600 hover:bg-indigo-700"])},a(d.value==="lost_reason"?"Add Lost Reason":"Add Comment"),11,fe)])])],32)])):x("",!0)]))}};var Se=L(we,[["__scopeId","data-v-5ed33695"]]);export{$e as C,Se as L};
