import{Q as j,T as S,j as $,c as B,A as E,a as c,b as n,u as r,w as p,F as u,d as m,Z as M,e as s,x as N,r as v,y as U,m as A,f as H,t as x}from"./app.0e820f21.js";import{_ as R,a as T}from"./AdminLayout.687face1.js";import{_ as D,a as F}from"./TextInput.a134c4d6.js";import{_ as b}from"./InputLabel.c491b164.js";import{P as O}from"./PrimaryButton.259b896f.js";import{_ as y}from"./Checkbox.a38f6303.js";import"./plugin-vue_export-helper.21dcd24c.js";const L={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},Q={class:"border-b border-gray-900/10 pb-12"},Z={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},q={class:"sm:col-span-12 grid grid-cols-6 gap-6"},z={class:"col-span-2"},G={class:"sm:col-span-12"},I={class:"sm:col-span-3"},J={class:"flex justify-between items-center border px-4 py-2 bg-gray-50 rounded-lg"},K={class:"flex items-center text-lg font-semibold leading-7 text-gray-900 space-x-2"},W={class:"border border-t-0 rounded-b-lg"},X={class:"text-sm font-semibold leading-6 text-gray-900 p-1"},Y={class:"flex justify-end p-1"},ee={class:"flex mt-6 items-center justify-between"},se={class:"ml-auto flex items-center justify-end gap-x-6"},te={key:0,class:"text-sm text-gray-600"},ce={__name:"Edit",props:["data","roleHasPermissions"],setup(f){const d=f,_=j().props.role,t=S({name:_.name,id:_.id,permissions:d.roleHasPermissions});$({});const k=a=>t.permissions.includes(a),w=(a,e)=>{if(a)t.permissions.push(e);else{const o=t.permissions.indexOf(e);o!==-1&&t.permissions.splice(o,1)}h()},C=(a,e)=>{const o=a.target.checked,i=new Set(t.permissions);e.forEach(l=>{o?i.add(l.id):i.delete(l.id)}),t.permissions=Array.from(i)},g=B(()=>{const a={};return Object.keys(d.data).forEach(e=>{const o=d.data[e].every(i=>t.permissions.includes(i.id));a[e]=o}),a}),h=a=>{for(const e in d.data){const o=d.data[e].every(i=>t.permissions.includes(i.id));g.value[e]=o}};return E(t.permissions,(a,e)=>{for(const o in d.data)h()},{deep:!0}),(a,e)=>(m(),c(u,null,[n(r(M),{title:"Update Role-Permission"}),n(R,null,{default:p(()=>[s("div",L,[e[6]||(e[6]=s("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Role & Permission",-1)),s("form",{onSubmit:e[2]||(e[2]=N(o=>r(t).patch(a.route("roles.update",{role:r(t).id})),["prevent"]))},[s("div",Q,[s("div",Z,[s("div",q,[s("div",z,[n(b,{for:"name",value:"Role Name"}),n(D,{id:"name",type:"text",modelValue:r(t).name,"onUpdate:modelValue":e[0]||(e[0]=o=>r(t).name=o),onChange:e[1]||(e[1]=o=>r(t).validate("name"))},null,8,["modelValue"]),n(F,{class:"",message:r(t).errors.name},null,8,["message"])])]),s("div",G,[n(b,{for:"name",value:"Select Permission"})]),(m(!0),c(u,null,v(f.data,(o,i)=>(m(),c("div",I,[s("div",J,[s("h3",K,[n(y,{checked:g.value[i],onChange:l=>C(l,o)},null,8,["checked","onChange"]),s("span",null,x(i),1)]),e[3]||(e[3]=s("div",{class:"cursor-pointer"},[s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})])],-1))]),s("div",W,[(m(!0),c(u,null,v(o,(l,P)=>(m(),c("div",{key:P,class:"flex justify-between items-center px-4 py-1 border-b last:border-b-0"},[s("div",X,x(l.name),1),s("div",Y,[n(y,{checked:k(l.id),"onUpdate:checked":V=>w(V,l.id),name:"permissions"},null,8,["checked","onUpdate:checked"])])]))),128))])]))),256))])]),s("div",ee,[s("div",se,[n(T,{href:a.route("roles.index")},{svg:p(()=>e[4]||(e[4]=[s("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel ",-1)])),_:1},8,["href"]),n(O,{disabled:r(t).processing},{default:p(()=>e[5]||(e[5]=[U("Update")])),_:1,__:[5]},8,["disabled"]),n(A,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[r(t).recentlySuccessful?(m(),c("p",te,"Saved.")):H("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{ce as default};
