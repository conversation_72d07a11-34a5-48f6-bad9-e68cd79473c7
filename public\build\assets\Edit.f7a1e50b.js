import{_ as o}from"./AdminLayout.687face1.js";/* empty css                                                          */import{a as m,b as s,u as r,w as i,F as l,d as n,Z as u,e as t}from"./app.0e820f21.js";import d from"./UpdateProfileInformationForm.3b597faa.js";import"./plugin-vue_export-helper.21dcd24c.js";import"./TextInput.a134c4d6.js";import"./InputLabel.c491b164.js";import"./PrimaryButton.259b896f.js";import"./TextArea.3742605b.js";const c={class:""},f={class:"max-w-7xl mx-auto"},p={class:"p-4 sm:p-8 bg-white shadow sm:rounded-lg"},b={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(e){return(_,a)=>(n(),m(l,null,[s(r(u),{title:"Profile"}),s(o,null,{header:i(()=>a[0]||(a[0]=[t("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"},"Profile",-1)])),default:i(()=>[t("div",c,[t("div",f,[t("div",p,[s(d,{"must-verify-email":e.mustVerifyEmail,status:e.status,class:"max-w-xl"},null,8,["must-verify-email","status"])])])])]),_:1})],64))}};export{b as default};
