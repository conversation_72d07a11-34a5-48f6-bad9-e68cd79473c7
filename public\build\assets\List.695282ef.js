import{_ as h,b as k,a as _}from"./AdminLayout.687face1.js";import{_ as y}from"./CreateButton.f13c50e2.js";import{_ as C}from"./SecondaryButton.f2b207b7.js";import{D as R}from"./DangerButton.7efeecc5.js";import{M as B}from"./Modal.c671de5e.js";import{T as L,j as f,a as r,b as o,u as j,w as s,F as g,d as l,Z as M,e as t,y as d,f as u,r as N,t as $,p as E}from"./app.0e820f21.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const V={class:"animate-top"},z={class:"flex justify-between items-center"},A={class:"flex space-x-6 mt-4 sm:ml-10 sm:mt-0 sm:flex-none"},D={class:"flex justify-end w-20"},O={key:0,class:"flex justify-end"},P={class:"mt-8 overflow-x-auto sm:rounded-lg"},T={class:"shadow sm:rounded-lg"},F={class:"w-full text-sm text-left rtl:text-right text-gray-500"},I={key:0},S={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},H={class:"items-center px-4 py-2.5"},Z={class:"flex items-center justify-start gap-4"},q=["onClick"],G={key:1},J={class:"p-6"},K={class:"mt-6 flex justify-end"},rt={__name:"List",props:["roles","permissions"],setup(a){const p=a,w=L({}),m=f(!1),c=f(null),b=n=>{c.value=n,m.value=!0},x=()=>{m.value=!1},v=()=>{w.delete(route("roles.destroy",{id:c.value}),{onSuccess:()=>x()})};return(n,e)=>(l(),r(g,null,[o(j(M),{title:"Role-Permission"}),o(h,null,{default:s(()=>[t("div",V,[t("div",z,[e[2]||(e[2]=t("div",{class:"items-start"},[t("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Roles & Permissions")],-1)),t("div",A,[t("div",D,[o(y,{href:n.route("setting")},{default:s(()=>e[0]||(e[0]=[d(" Back ")])),_:1,__:[0]},8,["href"])]),a.permissions.canCreateRoles?(l(),r("div",O,[o(y,{href:n.route("roles.create")},{default:s(()=>e[1]||(e[1]=[d(" Add Role ")])),_:1,__:[1]},8,["href"])])):u("",!0)])]),t("div",P,[t("div",T,[t("table",F,[e[14]||(e[14]=t("thead",{class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},[t("tr",{class:"border-b-2"},[t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ROLE NAME"),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"}),t("th",{scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer"},"ACTION")])],-1)),p.roles&&p.roles.length>0?(l(),r("tbody",I,[(l(!0),r(g,null,N(p.roles,(i,Q)=>(l(),r("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:i.id},[t("td",S,$(i.name),1),e[7]||(e[7]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),e[8]||(e[8]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),e[9]||(e[9]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),e[10]||(e[10]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),e[11]||(e[11]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),e[12]||(e[12]=t("td",{class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap text-gray-900"},null,-1)),t("td",H,[t("div",Z,[o(k,{align:"right",width:"48"},{trigger:s(()=>e[3]||(e[3]=[t("button",{type:"button",title:"Open details",class:"p-1 rounded dark:text-gray-600 hover:bg-gray-100 focus:bg-gray-100"},[t("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[t("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)])),content:s(()=>[a.permissions.canEditRoles?(l(),E(_,{key:0,href:n.route("roles.edit",{id:i.id})},{svg:s(()=>e[4]||(e[4]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)])),text:s(()=>e[5]||(e[5]=[t("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1)])),_:2},1032,["href"])):u("",!0),a.permissions.canDeleteRoles?(l(),r("button",{key:1,type:"button",onClick:U=>b(i.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},e[6]||(e[6]=[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),t("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1)]),8,q)):u("",!0)]),_:2},1024)])])]))),128))])):(l(),r("tbody",G,e[13]||(e[13]=[t("tr",{class:"bg-white"},[t("td",{colspan:"3",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)])))])])])]),o(B,{show:m.value,onClose:x},{default:s(()=>[t("div",J,[e[17]||(e[17]=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this Role & Permissions? ",-1)),t("div",K,[o(C,{onClick:x},{default:s(()=>e[15]||(e[15]=[d(" Cancel")])),_:1,__:[15]}),o(R,{class:"ml-3",onClick:v},{default:s(()=>e[16]||(e[16]=[d(" Delete ")])),_:1,__:[16]})])])]),_:1},8,["show"])]),_:1})],64))}};export{rt as default};
