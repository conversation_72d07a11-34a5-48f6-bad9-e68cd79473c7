import{T as d,p as l,w as t,d as m,b as e,u as r,Z as p,e as a,y as u,g as f,x as c}from"./app.0e820f21.js";import{G as w}from"./GuestLayout.5f1289af.js";import{_ as g,a as _}from"./TextInput.a134c4d6.js";import{_ as x}from"./InputLabel.c491b164.js";import{P as y}from"./PrimaryButton.259b896f.js";import"./plugin-vue_export-helper.21dcd24c.js";const b={class:"flex justify-end mt-4"},T={__name:"ConfirmPassword",setup(P){const o=d({password:""}),i=()=>{o.post(route("password.confirm"),{onFinish:()=>o.reset()})};return(C,s)=>(m(),l(w,null,{default:t(()=>[e(r(p),{title:"Confirm Password"}),s[2]||(s[2]=a("h2",{class:"text-center text-2xl font-bold leading-9 tracking-tight text-gray-900"},"Confirm Password",-1)),s[3]||(s[3]=a("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),a("form",{onSubmit:c(i,["prevent"])},[a("div",null,[e(x,{for:"password",value:"Password"}),e(g,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:r(o).password,"onUpdate:modelValue":s[0]||(s[0]=n=>r(o).password=n),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(_,{class:"mt-2",message:r(o).errors.password},null,8,["message"])]),a("div",b,[e(y,{class:f(["",{"opacity-25":r(o).processing}]),disabled:r(o).processing},{default:t(()=>s[1]||(s[1]=[u(" Confirm ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2,3]}))}};export{T as default};
