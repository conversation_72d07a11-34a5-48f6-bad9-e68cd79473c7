import{j as f,c as U,a,b as i,u as h,w as d,F as C,d as l,Z as ue,e,l as $,B as ce,K as me,g as N,y as b,f as p,r as M,p as O,t as n,E as R,q as K,v as ge,N as Y}from"./app.0e820f21.js";import{_ as pe,b as ve,a as Z}from"./AdminLayout.687face1.js";import{_ as xe}from"./CreateButton.f13c50e2.js";import{_ as fe}from"./SecondaryButton.f2b207b7.js";import{D as he}from"./DangerButton.7efeecc5.js";import{M as Q}from"./Modal.c671de5e.js";import{s as be,_ as ye}from"./sortAndSearch.29e714e8.js";import{_ as T}from"./SearchableDropdownNew.0cffdca8.js";import{_ as D}from"./InputLabel.c491b164.js";import{_ as _e}from"./ArrowIcon.f2c2b1ba.js";import{L as we,C as ke}from"./LeadComments.afd04537.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const Ce={class:"animate-top"},Ae={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},Se={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},Me={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},Le={class:"flex rounded-lg border border-gray-200 bg-white"},Ve={key:0,class:"sm:flex-none"},$e={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},Ne={class:"flex justify-between mb-2"},De={class:"flex"},ze={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Ee={key:0,class:"sm:col-span-4"},Be={class:"relative mt-2"},je={class:"sm:col-span-4"},Ue={class:"relative mt-2"},Oe={class:"sm:col-span-4"},Te={class:"relative mt-2"},Ie={key:0,class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8"},Pe={class:"flex justify-between items-start"},Fe={class:"text-lg font-semibold text-gray-800"},He={class:"text-sm font-semibold text-gray-900"},We={class:"flex-shrink-0"},qe={key:0},Ge=["onUpdate:modelValue","onChange"],Re=["value"],Ke=["onClick"],Ye=["onClick"],Ze={class:"flex space-x-2 mt-2 justify-between items-center w-full"},Qe={class:"text-xs text-gray-700"},Xe={class:"text-xs text-gray-700"},Je={class:"text-xs text-gray-700"},et={class:"mt-2"},tt={class:"text-sm"},st={class:"text-sm"},ot={class:"text-sm"},lt={class:"flex space-x-2 mt-4"},nt=["onClick"],at={key:1,class:"mt-8 overflow-x-auto rounded-lg max-w-full"},it={class:"shadow rounded-lg"},rt={class:"w-full text-sm text-left rtl:text-right text-gray-500"},dt={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},ut={class:"border-b-2"},ct=["onClick"],mt={key:0},gt={class:"px-4 py-2.5 min-w-28"},pt={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},vt={class:"px-4 py-2.5"},xt={class:"px-4 py-2.5 min-w-28"},ft={class:"px-4 py-2.5 min-w-28"},ht={class:"px-4 py-2.5 min-w-32"},bt={class:"px-4 py-2.5 min-w-32"},yt={class:"px-4 py-2.5"},_t={key:0,class:"flex items-center space-x-2 w-full"},wt=["onUpdate:modelValue","onChange"],kt=["value"],Ct=["onClick"],At={key:1,class:"flex items-center space-x-2"},St=["onClick"],Mt={class:"px-4 py-2.5 min-w-28"},Lt={key:0,class:"px-4 py-2.5"},Vt={class:"items-center px-4 py-2.5"},$t={class:"flex items-center justify-start gap-4"},Nt=["onClick"],Dt={key:1},zt={class:"bg-white rounded-lg p-6 w-full"},Et={class:"p-6"},Bt={class:"mt-6 flex justify-end"},Zt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","status","isAdmin"],setup(u){const y=u,{form:z,search:L,sort:X,fetchData:jt,sortKey:J,sortDirection:ee}=be("leads.index"),E=f(!1),I=f(null),k=f("card"),B=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"}],te=U(()=>[{id:"",name:"All Agents"},...y.agents]),se=U(()=>[{id:"",name:"All Country"},...y.counties]),oe=U(()=>[{id:"",name:"All Status"},...B]),le=[{field:"lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!1,visible:!0},{field:"open_size",label:"OPEN SIZE",sortable:!0,visible:!0},{field:"box_style",label:"BOX STYLE",sortable:!0,visible:!0},{field:"email",label:"Email",sortable:!0,visible:!0},{field:"number",label:"Number",sortable:!0,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"created_at",label:"DATE",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:y.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],P=o=>{I.value=o,E.value=!0},j=()=>{E.value=!1},ne=()=>{z.delete(route("leads.destroy",{lead:I.value}),{onSuccess:()=>j()})},F=o=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",v=f(y.agent_id||""),x=f(y.county_id||""),w=f(y.status||""),A=f(""),m=f({}),ae=(o,s)=>{v.value=o,V(A.value,v.value,x.value,w.value)},ie=(o,s)=>{x.value=o,V(A.value,v.value,x.value,w.value)},re=(o,s)=>{w.value=o,V(A.value,v.value,x.value,w.value)},V=(o,s,t,c)=>{A.value=o,console.log("agentId",s),console.log("countyId",t),console.log("status",c);const _=s===""?null:s,r=t===""?null:t;z.get(route("leads.index",{search:o,agent_id:_,county_id:r,status:c}),{preserveState:!0})},H=(o,s)=>{m.value[o]=s},W=o=>{delete m.value[o]},q=(o,s)=>{Y.post(route("leads.update-status",o),{status:s},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete m.value[o];const c=new URLSearchParams(window.location.search).get("page")||1;Y.get(route("leads.index"),{search:A.value,agent_id:v.value===""?null:v.value,county_id:x.value===""?null:x.value,page:c},{preserveScroll:!0,preserveState:!0,only:["data"]})},onError:t=>{console.error("Update failed:",t),alert("Failed to update status. Please try again.")}})},S=f({show:!1,leadId:null,comments:[]}),de=o=>{S.value={show:!0,leadId:o.id,comments:o.comments||[]}},G=()=>{S.value={show:!1,leadId:null,comments:[]}};return(o,s)=>(l(),a(C,null,[i(h(ue),{title:"Leads"}),i(pe,null,{default:d(()=>[e("div",Ce,[e("div",Ae,[s[11]||(s[11]=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Leads")],-1)),e("div",Se,[e("div",Me,[s[7]||(s[7]=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),$(e("input",{type:"text","onUpdate:modelValue":s[0]||(s[0]=t=>me(L)?L.value=t:null),onInput:s[1]||(s[1]=t=>V(h(L),v.value,x.value,w.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for leads..."},null,544),[[ce,h(L)]])]),e("div",Le,[e("button",{onClick:s[2]||(s[2]=t=>k.value="card"),class:N(["px-3 py-2 text-sm font-medium rounded-l-lg",k.value==="card"?"bg-indigo-600 text-white":"text-gray-700 hover:bg-gray-50"])},s[8]||(s[8]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"})],-1)]),2),e("button",{onClick:s[3]||(s[3]=t=>k.value="table"),class:N(["px-3 py-2 text-sm font-medium rounded-r-lg",k.value==="table"?"bg-indigo-600 text-white":"text-gray-700 hover:bg-gray-50"])},s[9]||(s[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h18M3 14h18m-9-4v8m-7 0V4a1 1 0 011-1h3M3 20h18a1 1 0 001-1V5a1 1 0 00-1-1H3a1 1 0 00-1 1v14a1 1 0 001 1z"})],-1)]),2)]),u.permissions.canCreateLead?(l(),a("div",Ve,[i(xe,{href:o.route("leads.create")},{default:d(()=>s[10]||(s[10]=[b(" Add Lead ")])),_:1,__:[10]},8,["href"])])):p("",!0)])]),e("div",$e,[e("div",Ne,[e("div",De,[s[12]||(s[12]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),i(D,{for:"customer_id",value:"Filters"})])]),e("div",ze,[y.isAdmin?(l(),a("div",Ee,[i(D,{for:"agent_filter",value:"Agents"}),e("div",Be,[i(T,{options:te.value,modelValue:v.value,"onUpdate:modelValue":s[4]||(s[4]=t=>v.value=t),onOnchange:ae},null,8,["options","modelValue"])])])):p("",!0),e("div",je,[i(D,{for:"county_filter",value:"Country"}),e("div",Ue,[i(T,{options:se.value,modelValue:x.value,"onUpdate:modelValue":s[5]||(s[5]=t=>x.value=t),onOnchange:ie},null,8,["options","modelValue"])])]),e("div",Oe,[i(D,{for:"county_filter",value:"Status"}),e("div",Te,[i(T,{options:oe.value,modelValue:w.value,"onUpdate:modelValue":s[6]||(s[6]=t=>w.value=t),onOnchange:re},null,8,["options","modelValue"])])])])]),k.value==="card"?(l(),a("div",Ie,[(l(!0),a(C,null,M(u.data.data,t=>{var c,_,r;return l(),a("div",{key:t.id,class:"bg-white p-5 rounded-lg shadow hover:shadow-md transition"},[e("div",Pe,[e("div",null,[e("h3",Fe,n(t.client_name),1),e("p",He,"Lead No: "+n(t.lead_number),1)]),e("div",We,[m.value[t.id]!==void 0?(l(),a("div",qe,[$(e("select",{"onUpdate:modelValue":g=>m.value[t.id]=g,onChange:g=>q(t.id,m.value[t.id]),class:"text-sm border-gray-300 rounded px-2 py-1 mt-1"},[(l(),a(C,null,M(B,g=>e("option",{key:g.id,value:g.id},n(g.name),9,Re)),64))],40,Ge),[[R,m.value[t.id]]]),e("button",{onClick:g=>W(t.id),class:"text-gray-400 ml-1 hover:text-gray-600"},"\u2715",8,Ke)])):(l(),a("span",{key:1,class:N(["px-3 py-1 rounded-full text-xs font-semibold cursor-pointer mt-1 inline-block",F(t.status)]),onClick:g=>H(t.id,t.status),title:"Click to edit status"},n(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,Ye))])]),e("div",Ze,[e("p",Qe,"Country: "+n(t.county?t.county.name:"N/A"),1),e("p",Xe,"Agent: "+n(((c=t.creator)==null?void 0:c.first_name)||"N/A"),1),e("p",Je,"Date: "+n(new Date(t.created_at).toLocaleDateString("en-GB")),1)]),e("div",et,[e("p",tt,[s[13]||(s[13]=e("strong",null,"Open Size:",-1)),b(" "+n(t.open_size),1)]),e("p",st,[s[14]||(s[14]=e("strong",null,"Email:",-1)),b(" "+n((_=t.email)!=null?_:"-"),1)]),e("p",ot,[s[15]||(s[15]=e("strong",null,"Number:",-1)),b(" "+n((r=t.number)!=null?r:"-"),1)])]),i(ke,{comments:t.comments||[],"current-user-id":o.$page.props.auth.user.id,"is-admin":o.$page.props.auth.user.role_id===1,onAddComment:g=>de(t)},null,8,["comments","current-user-id","is-admin","onAddComment"]),e("div",lt,[i(h(K),{href:o.route("leads.show",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"},{default:d(()=>s[16]||(s[16]=[b(" View ")])),_:2,__:[16]},1032,["href"]),i(h(K),{href:o.route("leads.edit",t.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100"},{default:d(()=>s[17]||(s[17]=[b(" Edit ")])),_:2,__:[17]},1032,["href"]),u.permissions.canDeleteLead?(l(),a("button",{key:0,onClick:g=>P(t.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100"}," Delete ",8,nt)):p("",!0)])])}),128))])):p("",!0),k.value==="table"?(l(),a("div",at,[e("div",it,[e("table",rt,[e("thead",dt,[e("tr",ut,[(l(),a(C,null,M(le,(t,c)=>$(e("th",{key:c,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:_=>h(X)(t.field,t.sortable)},[b(n(t.label)+" ",1),t.sortable?(l(),O(_e,{key:0,isSorted:h(J)===t.field,direction:h(ee)},null,8,["isSorted","direction"])):p("",!0)],8,ct),[[ge,t.visible]])),64))])]),u.data.data&&u.data.data.length>0?(l(),a("tbody",mt,[(l(!0),a(C,null,M(u.data.data,t=>{var c,_;return l(),a("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:t.id},[e("td",gt,n(t.lead_number),1),e("td",pt,n(t.client_name),1),e("td",vt,n(t.county?t.county.name:"N/A"),1),e("td",xt,n(t.open_size),1),e("td",ft,n(t.box_style),1),e("td",ht,n((c=t.email)!=null?c:"-"),1),e("td",bt,n((_=t.number)!=null?_:"-"),1),e("td",yt,[m.value[t.id]!==void 0?(l(),a("div",_t,[$(e("select",{"onUpdate:modelValue":r=>m.value[t.id]=r,class:"text-sm border-gray-300 rounded px-2 py-1",onChange:r=>q(t.id,m.value[t.id])},[(l(),a(C,null,M(B,r=>e("option",{class:"text-sm text-gray-900 text-bold",key:r.id,value:r.id},n(r.name),9,kt)),64))],40,wt),[[R,m.value[t.id]]]),e("button",{onClick:r=>W(t.id),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," \u2715 ",8,Ct)])):(l(),a("div",At,[e("span",{class:N(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",F(t.status)]),onClick:r=>H(t.id,t.status),title:"Click to edit status"},n(t.status.charAt(0).toUpperCase()+t.status.slice(1)),11,St)]))]),e("td",Mt,n(new Date(t.created_at).toLocaleDateString("en-GB")),1),y.isAdmin?(l(),a("td",Lt,n(t.creator?t.creator.first_name:"N/A"),1)):p("",!0),e("td",Vt,[e("div",$t,[i(ve,{align:"right",width:"48"},{trigger:d(()=>s[18]||(s[18]=[e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)])),content:d(()=>[i(Z,{href:o.route("leads.show",{id:t.id})},{svg:d(()=>s[19]||(s[19]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1)])),text:d(()=>s[20]||(s[20]=[e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1)])),_:2},1032,["href"]),u.permissions.canEditLead?(l(),O(Z,{key:0,href:o.route("leads.edit",{id:t.id})},{svg:d(()=>s[21]||(s[21]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)])),text:d(()=>s[22]||(s[22]=[e("span",{class:"text-sm text-gray-700 leading-5"}," Edit ",-1)])),_:2},1032,["href"])):p("",!0),u.permissions.canDeleteLead?(l(),a("button",{key:1,type:"button",onClick:r=>P(t.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},s[23]||(s[23]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5","x-tooltip":"tooltip"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),e("span",{class:"text-sm text-gray-700 leading-5"}," Delete ",-1)]),8,Nt)):p("",!0)]),_:2},1024)])])])}),128))])):(l(),a("tbody",Dt,s[24]||(s[24]=[e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)])))])])])):p("",!0),u.data.data&&u.data.data.length>0?(l(),O(ye,{key:2,class:"mt-6",links:u.data.links},null,8,["links"])):p("",!0)]),i(Q,{show:S.value.show,onClose:G},{default:d(()=>[e("div",zt,[s[25]||(s[25]=e("h3",{class:"text-lg font-medium mb-4"},"Add Comment",-1)),i(we,{"lead-id":S.value.leadId,comments:S.value.comments,"current-user-id":o.$page.props.auth.user.id,"is-admin":o.$page.props.auth.user.role_id===1,onClose:G},null,8,["lead-id","comments","current-user-id","is-admin"])])]),_:1},8,["show"]),i(Q,{show:E.value,onClose:j},{default:d(()=>[e("div",Et,[s[28]||(s[28]=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this lead? ",-1)),e("div",Bt,[i(fe,{onClick:j},{default:d(()=>s[26]||(s[26]=[b("Cancel")])),_:1,__:[26]}),i(he,{class:"ml-3",onClick:ne,disabled:h(z).processing},{default:d(()=>s[27]||(s[27]=[b(" Delete Lead ")])),_:1,__:[27]},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{Zt as default};
