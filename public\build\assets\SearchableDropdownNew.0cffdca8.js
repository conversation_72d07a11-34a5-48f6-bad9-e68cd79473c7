import{j as r,A as w,h as V,i as z,d as u,a as c,l as _,B as D,e as a,F as E,r as L,g as f,t as S,f as y}from"./app.0e820f21.js";const F={key:0,class:"absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm cursor-pointer",id:"options",role:"listbox","aria-labelledby":"combobox"},N=["onClick","onMouseenter"],A={__name:"SearchableDropdownNew",props:["options","modelValue","editMode"],emits:["onchange"],setup(k,{emit:M}){const n=k,g=M,p=r(n.options),o=r(""),d=r(!1),i=r(-1),m=r(!1),v=()=>{const t=new RegExp(o.value,"i");p.value=n.options.filter(e=>t.test(e.name))},h=()=>{o.value="",v()},O=(t,e)=>{o.value=t,d.value=!1,m.value=!1,g("update:modelValue",e),g("onchange",e,t)},b=()=>{n.editMode||(d.value=!0),m.value||h()},B=t=>{i.value=t};w(()=>n.options,()=>{v()}),w(()=>n.modelValue,t=>{const e=n.options.find(l=>l.id===t);e&&(o.value=e.name)}),V(()=>{const t=n.options.find(e=>e.id===n.modelValue);t?(o.value=t.name,m.value=!1):h(),document.addEventListener("click",x)}),z(()=>{document.removeEventListener("click",x)});const x=t=>{t.target.closest(".relative")||(d.value=!1)};return(t,e)=>(u(),c("div",null,[_(a("input",{id:"combobox",type:"text",placeholder:"Search...",role:"combobox","onUpdate:modelValue":e[0]||(e[0]=l=>o.value=l),onInput:v,onFocus:b,autocomplete:"off",class:"w-full rounded-md border-0 bg-white py-1.5 pl-3 pr-7 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,544),[[D,o.value]]),a("button",{type:"button",class:"absolute inset-y-0 right-0 flex items-center rounded-r-md px-2 focus:outline-none",onClick:b},e[1]||(e[1]=[a("svg",{class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[a("path",{"fill-rule":"evenodd",d:"M10 3a.75.75 0 01.55.24l3.25 3.5a.75.75 0 11-1.1 1.02L10 4.852 7.3 7.76a.75.75 0 01-1.1-1.02l3.25-3.5A.75.75 0 0110 3zm-3.76 9.2a.75.75 0 011.06.04l2.7 2.908 2.7-2.908a.75.75 0 111.1 1.02l-3.25 3.5a.75.75 0 01-1.1 0l-3.25-3.5a.75.75 0 01.04-1.06z","clip-rule":"evenodd"})],-1)])),d.value&&p.value.length?(u(),c("ul",F,[(u(!0),c(E,null,L(p.value,(l,s)=>(u(),c("li",{class:f(["relative cursor-default select-none py-2 pl-3 pr-9 cursor-pointer",{"text-white bg-indigo-600":i.value===s,"text-gray-900":i.value!==s}]),key:s,onClick:C=>O(l.name,l.id),onMouseenter:C=>B(s),tabindex:"-1",role:"option"},[a("span",{class:f(["block truncate",{"font-semibold":l.name===o.value}])},S(l.name),3),l.name===o.value?(u(),c("span",{key:0,class:f(["absolute inset-y-0 right-0 flex items-center pr-4",{"text-white":i.value===s,"text-indigo-600":i.value!==s}])},e[2]||(e[2]=[a("svg",{class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[a("path",{"fill-rule":"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z","clip-rule":"evenodd"})],-1)]),2)):y("",!0)],42,N))),128))])):y("",!0)]))}};export{A as _};
