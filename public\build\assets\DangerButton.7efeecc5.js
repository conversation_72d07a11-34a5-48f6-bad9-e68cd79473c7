import{_ as r}from"./plugin-vue_export-helper.21dcd24c.js";import{d as t,a as o,k as n}from"./app.0e820f21.js";const s={},a={class:"inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 active:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150"};function i(e,c){return t(),o("button",a,[n(e.$slots,"default")])}var u=r(s,[["render",i]]);export{u as D};
