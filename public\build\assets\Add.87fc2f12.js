import{T as V,j,c as P,A as B,a as d,b as a,u as l,w as p,F as u,d as c,Z as N,e as t,x as $,r as h,y as A,m as M,f as E,t as x}from"./app.0e820f21.js";import{_ as F,a as T}from"./AdminLayout.687face1.js";import{_ as O,a as R}from"./TextInput.a134c4d6.js";import{_ as b}from"./InputLabel.c491b164.js";import{P as U}from"./PrimaryButton.259b896f.js";import{_ as v}from"./Checkbox.a38f6303.js";import"./plugin-vue_export-helper.21dcd24c.js";const L={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},q={class:"border-b border-gray-900/10 pb-12"},D={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},Z={class:"sm:col-span-12 grid grid-cols-6 gap-6"},z={class:"col-span-2"},G={class:"sm:col-span-12"},H={class:"flex justify-between items-center border px-4 py-2 bg-gray-50 rounded-lg"},I={class:"flex items-center text-lg font-semibold leading-7 text-gray-900 space-x-2"},J={class:"border border-t-0 rounded-b-lg"},K={class:"text-sm font-semibold leading-6 text-gray-900 p-1"},Q={class:"flex justify-end p-1"},W={class:"flex mt-6 items-center justify-between"},X={class:"ml-auto flex items-center justify-end gap-x-6"},Y={key:0,class:"text-sm text-gray-600"},re={__name:"Add",props:["data"],setup(f){const e=V({name:"",permissions:[]}),y=()=>{console.log("Form data before submit:",{name:e.name,permissions:e.permissions,permissionsType:typeof e.permissions,permissionsLength:e.permissions.length}),e.post("/roles",{preserveScroll:!0,onSuccess:i=>{console.log("Success response:",i),e.reset()},onError:i=>{console.log("Form errors:",i)},onBefore:()=>{console.log("About to send request with data:",e.data())}})};j({});const k=(i,s)=>{if(i)e.permissions.push(s);else{const o=e.permissions.indexOf(s);o!==-1&&e.permissions.splice(o,1)}_()},m=f,w=(i,s)=>{const o=i.target.checked;s.forEach(n=>{if(o&&!e.permissions.includes(n.id))e.permissions.push(n.id);else if(!o&&e.permissions.includes(n.id)){const r=e.permissions.indexOf(n.id);r>-1&&e.permissions.splice(r,1)}})},g=P(()=>{const i={};return Object.keys(m.data).forEach(s=>{const o=m.data[s].every(n=>e.permissions.includes(n.id));i[s]=o}),i}),_=i=>{for(const s in m.data){const o=m.data[s].every(n=>e.permissions.includes(n.id));g.value[s]=o}};return B(e.permissions,(i,s)=>{for(const o in m.data)_()},{deep:!0}),(i,s)=>(c(),d(u,null,[a(l(N),{title:"Role & Permission"}),a(F,null,{default:p(()=>[t("div",L,[s[4]||(s[4]=t("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Add New Role & Permission ",-1)),t("form",{onSubmit:$(y,["prevent"]),class:""},[t("div",q,[t("div",D,[t("div",Z,[t("div",z,[a(b,{for:"name",value:"Role Name"}),a(O,{id:"name",type:"text",modelValue:l(e).name,"onUpdate:modelValue":s[0]||(s[0]=o=>l(e).name=o)},null,8,["modelValue"]),a(R,{message:l(e).errors.name},null,8,["message"])])]),t("div",G,[a(b,{for:"name",value:"Select Permission"})]),(c(!0),d(u,null,h(f.data,(o,n)=>(c(),d("div",{class:"sm:col-span-3",key:n},[t("div",H,[t("h3",I,[a(v,{checked:g.value[n],onChange:r=>w(r,o)},null,8,["checked","onChange"]),t("span",null,x(n),1)]),s[1]||(s[1]=t("div",{class:"cursor-pointer"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",class:"h-5 w-5"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 10h16M4 14h16M4 18h16"})])],-1))]),t("div",J,[(c(!0),d(u,null,h(o,(r,S)=>(c(),d("div",{key:S,class:"flex justify-between items-center px-4 py-1 border-b last:border-b-0"},[t("div",K,x(r.name),1),t("div",Q,[a(v,{checked:l(e).permissions.includes(r.id),"onUpdate:checked":C=>k(C,r.id),name:"permissions"},null,8,["checked","onUpdate:checked"])])]))),128))])]))),128))])]),t("div",W,[t("div",X,[a(T,{href:i.route("roles.index")},{svg:p(()=>s[2]||(s[2]=[t("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel ",-1)])),_:1},8,["href"]),a(U,{disabled:l(e).processing},{default:p(()=>s[3]||(s[3]=[A("Save")])),_:1,__:[3]},8,["disabled"]),a(M,{"enter-active-class":"transition ease-in-out","enter-from-class":"opacity-0","leave-active-class":"transition ease-in-out","leave-to-class":"opacity-0"},{default:p(()=>[l(e).recentlySuccessful?(c(),d("p",Y,"Saved.")):E("",!0)]),_:1})])])],32)])]),_:1})],64))}};export{re as default};
