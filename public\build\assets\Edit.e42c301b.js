import{T as S,a as p,b as o,u as s,w as u,F as T,d as g,Z as h,e as a,q as U,y as D,x as O}from"./app.0e820f21.js";import{_ as $,a as A}from"./AdminLayout.687face1.js";import{_ as i}from"./InputLabel.c491b164.js";import{a as r,_}from"./TextInput.a134c4d6.js";import{_ as c}from"./TextArea.3742605b.js";import{_ as m}from"./SearchableDropdownNew.0cffdca8.js";import{P as C}from"./PrimaryButton.259b896f.js";import"./plugin-vue_export-helper.21dcd24c.js";const B={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},P={class:"flex justify-between items-center mb-6"},j={class:"flex space-x-2"},I={class:"grid grid-cols-1 md:grid-cols-12 gap-6"},N={class:"md:col-span-3"},q={class:"relative mt-2"},E={class:"md:col-span-3"},F={class:"md:col-span-3"},L={class:"relative mt-2"},M={class:"md:col-span-3"},Q={class:"relative mt-2"},W={class:"md:col-span-6"},Z={class:"md:col-span-6"},z={class:"md:col-span-4"},G={class:"md:col-span-4"},H={class:"relative mt-2"},J={class:"md:col-span-4"},K={class:"relative mt-2"},R={class:"bg-gray-50 p-4 rounded-lg"},X={class:"grid grid-cols-2 md:grid-cols-4 gap-2"},Y={class:"flex mt-6 items-center justify-between"},ee={class:"ml-auto flex items-center justify-end gap-x-6"},te={key:0},se={key:1},ue={__name:"Edit",props:{task:Object,users:Array,types:Array,priority:Array,leads:Array,status:Array},setup(y){const l=y,e=S({title:l.task.title||"",description:l.task.description||"",type:l.task.type||"",priority:l.task.priority||"medium",status:l.task.status||"pending",due_date:l.task.due_date?new Date(l.task.due_date).toISOString().slice(0,16):"",reminder_date:l.task.reminder_date?new Date(l.task.reminder_date).toISOString().slice(0,16):"",assigned_to:l.task.assigned_to||"",lead_id:l.task.lead_id||null,notes:l.task.notes||""}),f=(n,t)=>e.lead_id=n,v=(n,t)=>e.type=n,b=(n,t)=>e.priority=n,x=(n,t)=>e.assigned_to=n,k=(n,t)=>e.status=n,V=()=>{const n=new Date(e.due_date);if(n.setDate(n.getDate()+1),e.due_date=n.toISOString().slice(0,16),e.reminder_date){const t=new Date(e.reminder_date);t.setDate(t.getDate()+1),e.reminder_date=t.toISOString().slice(0,16)}},w=()=>{e.put(route("tasks.update",l.task.id),{preserveScroll:!0})};return(n,t)=>(g(),p(T,null,[o(s(h),{title:"Tasks"}),o($,null,{default:u(()=>[a("div",B,[a("div",P,[t[13]||(t[13]=a("div",null,[a("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Edit Task"),a("p",{class:"text-sm text-gray-600 mt-1"},"Update task details and status")],-1)),a("div",j,[o(s(U),{href:n.route("tasks.index"),class:"px-4 py-2 bg-slate-100 border border-transparent rounded-md text-sm font-semibold leading-6 text-gray-900 hover:bg-gray-100"},{default:u(()=>t[12]||(t[12]=[D(" \u2190 Back to Tasks ")])),_:1,__:[12]},8,["href"])])]),a("form",{onSubmit:O(w,["prevent"]),class:"space-y-6"},[a("div",I,[a("div",N,[o(i,{for:"lead_id",value:"Lead"}),a("div",q,[o(m,{options:l.leads,modelValue:s(e).lead_id,"onUpdate:modelValue":t[0]||(t[0]=d=>s(e).lead_id=d),onOnchange:f},null,8,["options","modelValue"])]),o(r,{message:s(e).errors.lead_id},null,8,["message"])]),a("div",E,[o(i,{for:"title",value:"Task Title *"}),o(_,{id:"title",modelValue:s(e).title,"onUpdate:modelValue":t[1]||(t[1]=d=>s(e).title=d),type:"text",required:"",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{message:s(e).errors.title},null,8,["message"])]),a("div",F,[o(i,{for:"type",value:"Task Type *"}),a("div",L,[o(m,{options:l.types,modelValue:s(e).type,"onUpdate:modelValue":t[2]||(t[2]=d=>s(e).type=d),onOnchange:v},null,8,["options","modelValue"])]),o(r,{message:s(e).errors.type},null,8,["message"])]),a("div",M,[o(i,{for:"priority",value:"Priority *"}),a("div",Q,[o(m,{options:l.priority,modelValue:s(e).priority,"onUpdate:modelValue":t[3]||(t[3]=d=>s(e).priority=d),onOnchange:b},null,8,["options","modelValue"])]),o(r,{message:s(e).errors.priority},null,8,["message"])]),a("div",W,[o(i,{for:"description",value:"Description"}),o(c,{id:"description",modelValue:s(e).description,"onUpdate:modelValue":t[4]||(t[4]=d=>s(e).description=d),rows:3},null,8,["modelValue"]),o(r,{message:s(e).errors.description},null,8,["message"])]),a("div",Z,[o(i,{for:"notes",value:"Additional Notes"}),o(c,{id:"notes",modelValue:s(e).notes,"onUpdate:modelValue":t[5]||(t[5]=d=>s(e).notes=d),rows:3},null,8,["modelValue"]),o(r,{message:s(e).errors.notes},null,8,["message"])]),a("div",z,[o(i,{for:"due_date",value:"Due Date & Time *"}),o(_,{id:"due_date",type:"datetime-local",modelValue:s(e).due_date,"onUpdate:modelValue":t[6]||(t[6]=d=>s(e).due_date=d),required:"",class:"mt-1 block w-full"},null,8,["modelValue"]),o(r,{message:s(e).errors.due_date},null,8,["message"])]),a("div",G,[o(i,{for:"assigned_to",value:"Assign To *"}),a("div",H,[o(m,{options:l.users,modelValue:s(e).assigned_to,"onUpdate:modelValue":t[7]||(t[7]=d=>s(e).assigned_to=d),onOnchange:x},null,8,["options","modelValue"])]),o(r,{message:s(e).errors.assigned_to},null,8,["message"])]),a("div",J,[o(i,{for:"status",value:"Status *"}),a("div",K,[o(m,{options:l.status,modelValue:s(e).status,"onUpdate:modelValue":t[8]||(t[8]=d=>s(e).status=d),onOnchange:k},null,8,["options","modelValue"])]),o(r,{message:s(e).errors.status},null,8,["message"])])]),a("div",R,[t[14]||(t[14]=a("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"Quick Status Updates:",-1)),a("div",X,[a("button",{type:"button",onClick:t[9]||(t[9]=d=>s(e).status="in_progress"),class:"px-3 py-2 text-xs bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200"},"\u{1F504} Start Working"),a("button",{type:"button",onClick:t[10]||(t[10]=d=>s(e).status="completed"),class:"px-3 py-2 text-xs bg-green-100 text-green-800 rounded-md hover:bg-green-200"},"\u2705 Mark Complete"),a("button",{type:"button",onClick:V,class:"px-3 py-2 text-xs bg-yellow-100 text-yellow-800 rounded-md hover:bg-yellow-200"},"\u23F0 Postpone +1 Day"),a("button",{type:"button",onClick:t[11]||(t[11]=d=>s(e).status="cancelled"),class:"px-3 py-2 text-xs bg-red-100 text-red-800 rounded-md hover:bg-red-200"},"\u274C Cancel Task")])]),a("div",Y,[a("div",ee,[o(A,{href:n.route("tasks.index")},{svg:u(()=>t[15]||(t[15]=[a("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),o(C,{disabled:s(e).processing},{default:u(()=>[s(e).processing?(g(),p("span",te,"Updating...")):(g(),p("span",se,"Update"))]),_:1},8,["disabled"])])])],32)])]),_:1})],64))}};export{ue as default};
