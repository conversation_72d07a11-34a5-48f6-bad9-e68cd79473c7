import{j as h,T as Z,A as Y,a as _,b as l,u as o,w as P,F as ee,d as g,Z as te,e,t as r,f as v,x as ae,y as se}from"./app.0e820f21.js";import{_ as le,a as de}from"./AdminLayout.687face1.js";import{_ as C,a as x}from"./TextInput.a134c4d6.js";import{_ as u}from"./InputLabel.c491b164.js";import{P as oe}from"./PrimaryButton.259b896f.js";import{_ as ne}from"./TextArea.3742605b.js";import{_ as S}from"./Checkbox.a38f6303.js";import{_ as ie}from"./SearchableDropdownNew.0cffdca8.js";import"./plugin-vue_export-helper.21dcd24c.js";const re={class:"animate-top bg-white p-4 shadow sm:p-6 rounded-lg border"},ue={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6"},ce={class:"text-xl sm:text-2xl font-semibold leading-7 text-gray-900"},me={class:"text-sm text-gray-600 mt-1"},ye={class:"flex justify-start sm:justify-end"},_e={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"},ge={class:"mb-8 p-4 bg-gray-50 rounded-lg"},xe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ve={class:"text-sm text-gray-700"},pe={class:"text-sm text-gray-700"},qe={class:"text-sm font-semibold text-green-700"},fe={class:"text-sm text-gray-700"},be={class:"border-b border-gray-900/10 pb-12"},ke={class:"mt-10 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},he={class:"sm:col-span-12"},Ae={key:0,class:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg"},we={class:"text-sm text-red-600"},Ve={key:0,class:"sm:col-span-3"},Ce={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},Qe={class:"grid grid-cols-1 gap-4"},Ue={class:"text-sm text-gray-500 mt-1"},Ne={key:1,class:"sm:col-span-3"},$e={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},Se={class:"grid grid-cols-1 gap-4"},Fe={class:"text-sm text-gray-500 mt-1"},Pe={key:2,class:"sm:col-span-3"},Oe={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},De={class:"grid grid-cols-1 gap-4"},Be={class:"text-sm text-gray-500 mt-1"},je={key:3,class:"sm:col-span-3"},Me={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Ee={class:"grid grid-cols-1 gap-4"},Te={class:"text-sm text-gray-500 mt-1"},ze={key:4,class:"sm:col-span-12"},Le={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Ie={class:"flex items-center justify-between"},Ke={class:"text-lg font-semibold text-green-800"},Ge={class:"sm:col-span-12 mb-6"},He={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg"},Ze={class:"text-sm text-gray-700"},Je={class:"text-sm text-gray-700"},Re={class:"text-sm text-gray-700"},We={class:"text-sm text-gray-700"},Xe={class:"text-sm text-gray-700"},Ye={class:"text-sm text-gray-700"},et={class:"text-sm text-gray-700"},tt={class:"text-sm text-gray-700"},at={key:0},st={class:"text-sm text-gray-700"},lt={class:"sm:col-span-3"},dt={class:"sm:col-span-3"},ot={class:"relative mt-2"},nt={class:"sm:col-span-3"},it=["value"],rt={class:"sm:col-span-3"},ut=["value"],ct={class:"sm:col-span-12"},mt={class:"flex mt-6 items-center justify-between"},yt={class:"ml-auto flex items-center justify-end gap-x-6"},ht={__name:"Edit",props:{data:{type:Object,required:!0},productionStages:{type:Array,required:!0}},setup(s){const i=s;h(!0);const p=h(!!i.data.selected_qty_1),q=h(!!i.data.selected_qty_2),f=h(!!i.data.selected_qty_3),b=h(!!i.data.selected_qty_4),Q=h(0),a=Z({selected_qty_1:i.data.lead.qty_1||"",selected_qty_2:i.data.lead.qty_2||"",selected_qty_3:i.data.lead.qty_3||"",selected_qty_4:i.data.lead.qty_4||"",tracking_number:i.data.tracking_number||"",expected_delivery:i.data.expected_delivery||"",actual_delivery:i.data.actual_delivery||"",notes:i.data.notes||"",production_stage:i.data.production_stage}),J=()=>p.value||q.value||f.value||b.value?p.value&&!a.selected_qty_1?(alert("Please enter a value for Quantity 1."),!1):q.value&&!a.selected_qty_2?(alert("Please enter a value for Quantity 2."),!1):f.value&&!a.selected_qty_3?(alert("Please enter a value for Quantity 3."),!1):b.value&&!a.selected_qty_4?(alert("Please enter a value for Quantity 4."),!1):!0:(alert("Please select at least one quantity for the order."),!1),R=()=>{if(!J())return;const n=W();Z(n).put(route("orders.update",i.data.id),{preserveScroll:!0})},k=n=>{var w,V;const t={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},c=((V=(w=i.data.lead)==null?void 0:w.county)==null?void 0:V.name)||"UK",m=Object.keys(t).find($=>c.toLowerCase().includes($.toLowerCase())),{locale:y,currency:A}=t[m]||t.UK,N=new Intl.NumberFormat(y,{style:"currency",currency:A,currencyDisplay:"symbol"}).format(n);return`${A} ${N}`},F=()=>{var t,c,m,y;let n=0;p.value&&a.selected_qty_1&&((t=i.data.quotation)==null?void 0:t.price_qty_1)&&(n+=parseFloat(a.selected_qty_1)*parseFloat(i.data.quotation.price_qty_1)),q.value&&a.selected_qty_2&&((c=i.data.quotation)==null?void 0:c.price_qty_2)&&(n+=parseFloat(a.selected_qty_2)*parseFloat(i.data.quotation.price_qty_2)),f.value&&a.selected_qty_3&&((m=i.data.quotation)==null?void 0:m.price_qty_3)&&(n+=parseFloat(a.selected_qty_3)*parseFloat(i.data.quotation.price_qty_3)),b.value&&a.selected_qty_4&&((y=i.data.quotation)==null?void 0:y.price_qty_4)&&(n+=parseFloat(a.selected_qty_4)*parseFloat(i.data.quotation.price_qty_4)),Q.value=n},U=(n,t)=>{F()},W=()=>{var t,c,m,y;const n={status:a.status,tracking_number:a.tracking_number,expected_delivery:a.expected_delivery,actual_delivery:a.actual_delivery,production_stage:a.production_stage,notes:a.notes,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null,price_qty_1:null,price_qty_2:null,price_qty_3:null,price_qty_4:null};return p.value&&a.selected_qty_1&&(n.selected_qty_1=a.selected_qty_1,n.price_qty_1=(t=i.data.quotation)==null?void 0:t.price_qty_1),q.value&&a.selected_qty_2&&(n.selected_qty_2=a.selected_qty_2,n.price_qty_2=(c=i.data.quotation)==null?void 0:c.price_qty_2),f.value&&a.selected_qty_3&&(n.selected_qty_3=a.selected_qty_3,n.price_qty_3=(m=i.data.quotation)==null?void 0:m.price_qty_3),b.value&&a.selected_qty_4&&(n.selected_qty_4=a.selected_qty_4,n.price_qty_4=(y=i.data.quotation)==null?void 0:y.price_qty_4),n.total_amount=Q.value,n};Y([()=>a.selected_qty_1,()=>p.value,()=>a.selected_qty_2,()=>q.value,()=>a.selected_qty_3,()=>f.value,()=>a.selected_qty_4,()=>b.value],F),F();const X=(n,t)=>{a.production_stage=n};return(n,t)=>(g(),_(ee,null,[l(o(te),{title:"Orders"}),l(le,null,{default:P(()=>{var c,m,y,A,N,w,V,$,O,D,B,j,M,E,T,z,L,I,K,G,H;return[e("div",re,[e("div",ue,[e("div",null,[e("h2",ce," Edit Order - "+r(s.data.order_number),1),e("p",me," Client: "+r(s.data.lead.client_name),1)]),e("div",ye,[s.data.is_confirmed?(g(),_("span",_e," \u2713 Confirmed ")):v("",!0)])]),e("div",ge,[t[21]||(t[21]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Summary",-1)),e("div",xe,[e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm font-semibold text-gray-900"},"Quotation",-1)),e("p",ve,r(((c=s.data.quotation)==null?void 0:c.quotation_number)||"N/A"),1)]),e("div",null,[t[18]||(t[18]=e("p",{class:"text-sm font-semibold text-gray-900"},"Lead",-1)),e("p",pe,r(s.data.lead.lead_number||"N/A"),1)]),e("div",null,[t[19]||(t[19]=e("p",{class:"text-sm font-semibold text-gray-900"},"Total Amount",-1)),e("p",qe,r(k(s.data.total_amount)),1)]),e("div",null,[t[20]||(t[20]=e("p",{class:"text-sm font-semibold text-gray-900"},"Created By",-1)),e("p",fe,r((m=s.data.creator)==null?void 0:m.first_name)+" "+r((y=s.data.creator)==null?void 0:y.last_name),1)])])]),e("form",{onSubmit:ae(R,["prevent"])},[e("div",be,[e("div",ke,[e("div",he,[t[22]||(t[22]=e("h3",{class:"text-lg font-semibold text-gray-900"},"Edit Order Quantities",-1)),t[23]||(t[23]=e("p",{class:"text-sm text-gray-600"},'Current order quantities are shown. Check/uncheck to add/remove quantities. Use "Select All" to see all available options:',-1)),n.$page.props.errors.quantities?(g(),_("div",Ae,[e("p",we,r(n.$page.props.errors.quantities),1)])):v("",!0)]),((A=s.data.lead)==null?void 0:A.qty_1)&&((N=s.data.quotation)==null?void 0:N.price_qty_1)?(g(),_("div",Ve,[e("div",Ce,[l(S,{checked:p.value,"onUpdate:checked":[t[0]||(t[0]=d=>p.value=d),t[1]||(t[1]=d=>U(1,d))]},null,8,["checked"]),l(u,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",Qe,[e("div",null,[l(u,{for:"selected_qty_1",value:`Order Qty 1 (Available: ${s.data.lead.qty_1})`},null,8,["value"]),l(C,{id:"selected_qty_1",type:"number",modelValue:o(a).selected_qty_1,"onUpdate:modelValue":t[2]||(t[2]=d=>o(a).selected_qty_1=d),max:s.data.lead.qty_1,min:"1",placeholder:`Max: ${s.data.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(x,{message:o(a).errors.selected_qty_1},null,8,["message"]),e("p",Ue,"Price: "+r(k(s.data.quotation.price_qty_1))+" per unit",1)])])])):v("",!0),((w=s.data.lead)==null?void 0:w.qty_2)&&((V=s.data.quotation)==null?void 0:V.price_qty_2)?(g(),_("div",Ne,[e("div",$e,[l(S,{checked:q.value,"onUpdate:checked":[t[3]||(t[3]=d=>q.value=d),t[4]||(t[4]=d=>U(2,d))]},null,8,["checked"]),l(u,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",Se,[e("div",null,[l(u,{for:"selected_qty_2",value:`Order Qty 2 (Available: ${s.data.lead.qty_2})`},null,8,["value"]),l(C,{id:"selected_qty_2",type:"number",modelValue:o(a).selected_qty_2,"onUpdate:modelValue":t[5]||(t[5]=d=>o(a).selected_qty_2=d),max:s.data.lead.qty_2,min:"1",placeholder:`Max: ${s.data.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(x,{message:o(a).errors.selected_qty_2},null,8,["message"]),e("p",Fe,"Price: "+r(k(s.data.quotation.price_qty_2))+" per unit",1)])])])):v("",!0),(($=s.data.lead)==null?void 0:$.qty_3)&&((O=s.data.quotation)==null?void 0:O.price_qty_3)?(g(),_("div",Pe,[e("div",Oe,[l(S,{checked:f.value,"onUpdate:checked":[t[6]||(t[6]=d=>f.value=d),t[7]||(t[7]=d=>U(3,d))]},null,8,["checked"]),l(u,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",De,[e("div",null,[l(u,{for:"selected_qty_3",value:`Order Qty 3 (Available: ${s.data.lead.qty_3})`},null,8,["value"]),l(C,{id:"selected_qty_3",type:"number",modelValue:o(a).selected_qty_3,"onUpdate:modelValue":t[8]||(t[8]=d=>o(a).selected_qty_3=d),max:s.data.lead.qty_3,min:"1",placeholder:`Max: ${s.data.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(x,{message:o(a).errors.selected_qty_3},null,8,["message"]),e("p",Be,"Price: "+r(k(s.data.quotation.price_qty_3))+" per unit",1)])])])):v("",!0),((D=s.data.lead)==null?void 0:D.qty_4)&&((B=s.data.quotation)==null?void 0:B.price_qty_4)?(g(),_("div",je,[e("div",Me,[l(S,{checked:b.value,"onUpdate:checked":[t[9]||(t[9]=d=>b.value=d),t[10]||(t[10]=d=>U(4,d))]},null,8,["checked"]),l(u,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Ee,[e("div",null,[l(u,{for:"selected_qty_4",value:`Order Qty 4 (Available: ${s.data.lead.qty_4})`},null,8,["value"]),l(C,{id:"selected_qty_4",type:"number",modelValue:o(a).selected_qty_4,"onUpdate:modelValue":t[11]||(t[11]=d=>o(a).selected_qty_4=d),max:s.data.lead.qty_4,min:"1",placeholder:`Max: ${s.data.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),l(x,{message:o(a).errors.selected_qty_4},null,8,["message"]),e("p",Te,"Price: "+r(k(s.data.quotation.price_qty_4))+" per unit",1)])])])):v("",!0),Q.value>0?(g(),_("div",ze,[e("div",Le,[e("div",Ie,[e("div",null,[e("p",Ke," Updated Order Total: "+r(k(Q.value.toFixed(2))),1),t[24]||(t[24]=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing ",-1))]),t[25]||(t[25]=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1))])])])):v("",!0),e("div",Ge,[t[35]||(t[35]=e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information",-1)),e("div",He,[e("div",null,[t[26]||(t[26]=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1)),e("p",Ze,r(((j=s.data.lead)==null?void 0:j.client_name)||"N/A"),1)]),e("div",null,[t[27]||(t[27]=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1)),e("p",Je,r(((E=(M=s.data.lead)==null?void 0:M.county)==null?void 0:E.name)||"N/A"),1)]),e("div",null,[t[28]||(t[28]=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1)),e("p",Re,r(((T=s.data.lead)==null?void 0:T.dimensions)||"N/A"),1)]),e("div",null,[t[29]||(t[29]=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1)),e("p",We,r(((z=s.data.lead)==null?void 0:z.open_size)||"N/A"),1)]),e("div",null,[t[30]||(t[30]=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1)),e("p",Xe,r(((L=s.data.lead)==null?void 0:L.box_style)||"N/A"),1)]),e("div",null,[t[31]||(t[31]=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1)),e("p",Ye,r(((I=s.data.lead)==null?void 0:I.stock)||"N/A"),1)]),e("div",null,[t[32]||(t[32]=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1)),e("p",et,r(((K=s.data.lead)==null?void 0:K.lamination)||"N/A"),1)]),e("div",null,[t[33]||(t[33]=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1)),e("p",tt,r(((G=s.data.lead)==null?void 0:G.printing)||"N/A"),1)]),(H=s.data.lead)!=null&&H.add_ons?(g(),_("div",at,[t[34]||(t[34]=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1)),e("p",st,r(s.data.lead.add_ons),1)])):v("",!0)])]),e("div",lt,[l(u,{for:"tracking_number",value:"Tracking Number"}),l(C,{id:"tracking_number",type:"text",modelValue:o(a).tracking_number,"onUpdate:modelValue":t[12]||(t[12]=d=>o(a).tracking_number=d),placeholder:"Enter tracking number"},null,8,["modelValue"]),l(x,{message:o(a).errors.tracking_number},null,8,["message"]),t[36]||(t[36]=e("p",{class:"text-sm text-gray-500 mt-1"},"Add tracking number when order is shipped",-1))]),e("div",dt,[l(u,{for:"production_stage",value:"Production Stage"}),e("div",ot,[l(ie,{options:s.productionStages,modelValue:o(a).production_stage,"onUpdate:modelValue":t[13]||(t[13]=d=>o(a).production_stage=d),onOnchange:X},null,8,["options","modelValue"])]),l(x,{message:o(a).errors.production_stage},null,8,["message"])]),e("div",nt,[l(u,{for:"expected_delivery",value:"Expected Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(a).expected_delivery?o(a).expected_delivery.slice(0,10):"",onInput:t[14]||(t[14]=d=>o(a).expected_delivery=d.target.value)},null,40,it),l(x,{message:o(a).errors.expected_delivery},null,8,["message"])]),e("div",rt,[l(u,{for:"actual_delivery",value:"Actual Delivery Date"}),e("input",{type:"date",class:"mt-2 block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",value:o(a).actual_delivery?o(a).actual_delivery.slice(0,10):"",onInput:t[15]||(t[15]=d=>o(a).actual_delivery=d.target.value)},null,40,ut),l(x,{message:o(a).errors.actual_delivery},null,8,["message"])]),e("div",ct,[l(u,{for:"notes",value:"Order Notes"}),l(ne,{id:"notes",modelValue:o(a).notes,"onUpdate:modelValue":t[16]||(t[16]=d=>o(a).notes=d),rows:"4",placeholder:"Add any notes about this order..."},null,8,["modelValue"]),l(x,{message:o(a).errors.notes},null,8,["message"])])])]),e("div",mt,[e("div",yt,[l(de,{href:n.route("orders.index")},{svg:P(()=>t[37]||(t[37]=[e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),l(oe,{disabled:o(a).processing},{default:P(()=>t[38]||(t[38]=[se(" Save ")])),_:1,__:[38]},8,["disabled"])])])],32)])]}),_:1})],64))}};export{ht as default};
