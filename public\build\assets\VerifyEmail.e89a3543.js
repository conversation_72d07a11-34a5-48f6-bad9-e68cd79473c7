import{T as f,c as p,p as _,w as o,d as r,b as a,u as i,Z as g,e as t,a as y,f as v,y as n,g as x,q as b,x as k}from"./app.0e820f21.js";import{G as h}from"./GuestLayout.5f1289af.js";import{P as V}from"./PrimaryButton.259b896f.js";import{_ as w}from"./plugin-vue_export-helper.21dcd24c.js";const E={key:0,class:"mb-4 font-medium text-sm text-green-500"},B={class:"mt-4"},N={class:"text-center items-center mt-4"},C={__name:"VerifyEmail",props:{status:{type:String}},setup(d){const l=d,s=f({}),m=()=>{s.post(route("verification.send"))},u=p(()=>l.status==="verification-link-sent");return(c,e)=>(r(),_(h,null,{default:o(()=>[a(i(g),{title:"Email Verification"}),e[2]||(e[2]=t("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"},"Email Verification",-1)),e[3]||(e[3]=t("div",{class:"mb-4 mt-2 text-sm text-gray-500"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1)),u.value?(r(),y("div",E," A new verification link has been sent to the email address you provided during registration. ")):v("",!0),t("form",{onSubmit:k(m,["prevent"])},[t("div",B,[a(V,{class:x({"opacity-25":i(s).processing}),disabled:i(s).processing},{default:o(()=>e[0]||(e[0]=[n(" Resend Verification Email ")])),_:1,__:[0]},8,["class","disabled"])]),t("div",N,[a(i(b),{href:c.route("logout"),method:"post",as:"button",class:"font-semibold text-indigo-600 text-sm hover:text-indigo-500"},{default:o(()=>e[1]||(e[1]=[n("Log Out")])),_:1,__:[1]},8,["href"])])],32)]),_:1,__:[2,3]}))}};var I=w(C,[["__scopeId","data-v-53c36a92"]]);export{I as default};
