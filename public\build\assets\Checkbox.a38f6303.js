import{c as d,l as n,D as l,d as i,a as p}from"./app.0e820f21.js";const m=["value"],k={__name:"Checkbox",props:{checked:{type:[<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>],required:!0},value:{default:null}},emits:["update:checked"],setup(e,{emit:a}){const r=a,s=e,t=d({get(){return s.checked},set(o){r("update:checked",o)}});return(o,c)=>n((i(),p("input",{type:"checkbox",value:e.value,"onUpdate:modelValue":c[0]||(c[0]=u=>t.value=u),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,m)),[[l,t.value]])}};export{k as _};
