import{T as c,p as w,w as m,d as g,b as o,u as e,Z as _,e as r,y as f,g as y,x}from"./app.0e820f21.js";import{G as b}from"./GuestLayout.5f1289af.js";import{_ as l,a as d}from"./TextInput.a134c4d6.js";import{_ as i}from"./InputLabel.c491b164.js";import{P as v}from"./PrimaryButton.259b896f.js";import{_ as V}from"./plugin-vue_export-helper.21dcd24c.js";const P={class:"mt-2"},k={class:"mt-4"},R={class:"flex items-center justify-end mt-4"},h={__name:"ResetPassword",props:{email:{type:String,required:!0},token:{type:String,required:!0}},setup(p){const n=p,s=c({token:n.token,email:n.email,password:"",password_confirmation:""}),u=()=>{s.post(route("password.store"),{onFinish:()=>s.reset("password","password_confirmation")})};return(q,a)=>(g(),w(b,null,{default:m(()=>[o(e(_),{title:"Reset Password"}),a[4]||(a[4]=r("h2",{class:"text-center text-xl font-semibold leading-9 tracking-tight text-indigo-600"},"Reset Password",-1)),r("form",{onSubmit:x(u,["prevent"])},[r("div",null,[o(i,{for:"email",value:"Email"}),o(l,{id:"email",type:"email",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).email,"onUpdate:modelValue":a[0]||(a[0]=t=>e(s).email=t),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(d,{class:"mt-2",message:e(s).errors.email},null,8,["message"])]),r("div",P,[o(i,{for:"password",value:"Password"}),o(l,{id:"password",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password,"onUpdate:modelValue":a[1]||(a[1]=t=>e(s).password=t),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(d,{class:"mt-2",message:e(s).errors.password},null,8,["message"])]),r("div",k,[o(i,{for:"password_confirmation",value:"Confirm Password"}),o(l,{id:"password_confirmation",type:"password",class:"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 sm:text-sm sm:leading-6",modelValue:e(s).password_confirmation,"onUpdate:modelValue":a[2]||(a[2]=t=>e(s).password_confirmation=t),required:"",autocomplete:"new-password"},null,8,["modelValue"]),o(d,{class:"mt-2",message:e(s).errors.password_confirmation},null,8,["message"])]),r("div",R,[o(v,{class:y({"opacity-25":e(s).processing}),disabled:e(s).processing},{default:m(()=>a[3]||(a[3]=[f(" Reset Password ")])),_:1,__:[3]},8,["class","disabled"])])],32)]),_:1,__:[4]}))}};var G=V(h,[["__scopeId","data-v-2e9648ba"]]);export{G as default};
