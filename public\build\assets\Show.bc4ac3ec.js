import{j as v,a as l,b as m,u as B,w as u,F as y,d as a,Z as M,e as t,t as o,l as P,E as $,r as k,g as z,f as i,y as C,N as D}from"./app.0e820f21.js";import{_ as j,a as S}from"./AdminLayout.687face1.js";import{C as Q,L as V}from"./LeadComments.afd04537.js";import{M as E}from"./Modal.c671de5e.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const U={class:"animate-top"},O={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},F={class:"text-3xl font-bold text-gray-900"},H={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},R={class:"lg:col-span-2 space-y-8"},T={class:"bg-white shadow rounded-lg p-6"},W={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Z={class:"mt-1 text-sm text-gray-700"},G={class:"mt-1 text-sm text-gray-700"},J={class:"mt-1 text-sm text-gray-700"},K={class:"mt-1 text-sm text-gray-700"},X={key:0,class:"flex items-center space-x-2 w-full"},Y=["value"],tt={key:1,class:"flex items-center space-x-2"},et={class:"mt-1 text-sm text-gray-700"},st={class:"bg-white shadow rounded-lg p-6"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},at={class:"mt-1 text-sm text-gray-700"},ot={class:"mt-1 text-sm text-gray-700"},nt={class:"mt-1 text-sm text-gray-700"},dt={class:"mt-1 text-sm text-gray-700"},it={class:"mt-1 text-sm text-gray-700"},rt={class:"mt-1 text-sm text-gray-700"},mt={key:0,class:"md:col-span-2"},xt={class:"mt-1 text-sm text-gray-700"},ct={class:"bg-white shadow rounded-lg p-6"},ut={class:"overflow-x-auto"},gt={class:"min-w-full divide-y divide-gray-200"},yt={class:"bg-white divide-y divide-gray-200"},ft={key:0},pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},bt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},wt={key:0,class:"text-green-600 font-medium"},ht={key:1,class:"text-gray-400"},vt={class:"px-6 py-4 whitespace-nowrap"},kt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Ct={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},St={key:1},Lt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},_t={key:0,class:"text-green-600 font-medium"},Nt={key:1,class:"text-gray-400"},It={class:"px-6 py-4 whitespace-nowrap"},At={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Bt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Mt={key:2},Pt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},$t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},zt={key:0,class:"text-green-600 font-medium"},Dt={key:1,class:"text-gray-400"},jt={class:"px-6 py-4 whitespace-nowrap"},Qt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Vt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Et={key:3},Ut={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ot={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-700"},Ft={key:0,class:"text-green-600 font-medium"},Ht={key:1,class:"text-gray-400"},Rt={class:"px-6 py-4 whitespace-nowrap"},Tt={key:0,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800"},Wt={key:1,class:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800"},Zt={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},Gt={class:"flex items-center"},Jt={class:"text-xs text-green-600"},Kt={key:0,class:"bg-white shadow rounded-lg p-6"},Xt={class:"text-sm text-gray-700 whitespace-pre-wrap"},Yt={class:"space-y-6"},te={class:"bg-white shadow rounded-lg p-6"},ee={class:"space-y-3 w-full"},se={key:0,class:"bg-white shadow rounded-lg p-6"},le={class:"space-y-3"},ae={class:"flex items-center space-x-3"},oe={class:"text-sm text-gray-700"},ne=["href"],de={class:"bg-white shadow rounded-lg p-6"},ie={class:"bg-white shadow rounded-lg p-6"},re={class:"space-y-4"},me={class:"flex items-start space-x-3"},xe={class:"text-xs text-gray-500"},ce={key:0,class:"flex items-start space-x-3"},ue={class:"text-xs text-gray-500"},ge={class:"bg-white rounded-lg p-6 w-full"},ve={__name:"Show",props:{leads:{type:Object,required:!0}},setup(s){const L=n=>({new:"bg-blue-100 text-blue-800",contacted:"bg-purple-100 text-purple-800",quotation:"bg-yellow-100 text-yellow-800",negotiation:"bg-orange-100 text-orange-800",won:"bg-green-100 text-green-800",lost:"bg-red-100 text-red-800"})[n]||"bg-gray-100 text-gray-800",g=n=>n?new Date(n).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A",q=[{id:"new",name:"New"},{id:"contacted",name:"Contacted"},{id:"quotation",name:"Quotation"},{id:"negotiation",name:"Negotiation"},{id:"won",name:"Won"},{id:"lost",name:"Lost"}],r=v({}),_=(n,e)=>{D.post(route("leads.update-status",n),{status:e},{preserveScroll:!0,preserveState:!0,onSuccess:()=>{delete r.value[n],new URLSearchParams(window.location.search).get("page")},onError:c=>{console.error("Update failed:",c),alert("Failed to update status. Please try again.")}})},N=n=>{delete r.value[n]},I=(n,e)=>{r.value[n]=e},x=v({show:!1,leadId:null,comments:[]}),A=n=>{x.value={show:!0,leadId:n.id,comments:n.comments||[]}},f=()=>{x.value={show:!1,leadId:null,comments:[]}};return(n,e)=>(a(),l(y,null,[m(B(M),{title:"Leads"}),m(j,null,{default:u(()=>{var c,p,b,w,h;return[t("div",U,[t("div",O,[t("div",null,[t("h1",F,o(s.leads.lead_number),1),e[5]||(e[5]=t("p",{class:"text-gray-600 mt-1"},"Leads Details",-1))])]),t("div",H,[t("div",R,[t("div",T,[e[12]||(e[12]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Basic Information",-1)),t("div",W,[t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Client Name",-1)),t("p",Z,o(s.leads.client_name),1)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-semibold text-gray-900"},"County",-1)),t("p",G,o(((c=s.leads.county)==null?void 0:c.name)||"N/A"),1)]),t("div",null,[e[8]||(e[8]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Email",-1)),t("p",J,o((p=s.leads.email)!=null?p:"N/A"),1)]),t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Number",-1)),t("p",K,o((b=s.leads.number)!=null?b:"N/A"),1)]),t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1)),r.value[s.leads.id]!==void 0?(a(),l("div",X,[P(t("select",{"onUpdate:modelValue":e[0]||(e[0]=d=>r.value[s.leads.id]=d),class:"text-sm border-gray-300 rounded px-2 py-1",onChange:e[1]||(e[1]=d=>_(s.leads.id,r.value[s.leads.id]))},[(a(),l(y,null,k(q,d=>t("option",{class:"text-sm text-gray-900 text-bold",key:d.id,value:d.id},o(d.name),9,Y)),64))],544),[[$,r.value[s.leads.id]]]),t("button",{onClick:e[2]||(e[2]=d=>N(s.leads.id)),class:"text-gray-400 hover:text-gray-600 text-sm",title:"Cancel"}," \u2715 ")])):(a(),l("div",tt,[t("span",{class:z(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",L(s.leads.status)]),onClick:e[3]||(e[3]=d=>I(s.leads.id,s.leads.status)),title:"Click to edit status"},o(s.leads.status.charAt(0).toUpperCase()+s.leads.status.slice(1)),3)]))]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1)),t("p",et,o((w=s.leads.creator)==null?void 0:w.first_name)+" "+o((h=s.leads.creator)==null?void 0:h.last_name),1)])])]),t("div",st,[e[20]||(e[20]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Product Specifications",-1)),t("div",lt,[t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Dimensions",-1)),t("p",at,o(s.leads.dimensions),1)]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Open Size",-1)),t("p",ot,o(s.leads.open_size),1)]),t("div",null,[e[15]||(e[15]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Box Style",-1)),t("p",nt,o(s.leads.box_style),1)]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Stock",-1)),t("p",dt,o(s.leads.stock),1)]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Lamination",-1)),t("p",it,o(s.leads.lamination),1)]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Printing",-1)),t("p",rt,o(s.leads.printing),1)]),s.leads.add_ons?(a(),l("div",mt,[e[19]||(e[19]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Add-ons",-1)),t("p",xt,o(s.leads.add_ons),1)])):i("",!0)])]),t("div",ct,[e[28]||(e[28]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quantity Details",-1)),t("div",ut,[t("table",gt,[e[25]||(e[25]=t("thead",{class:"bg-gray-50"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Quantity"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Requested"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Confirmed"),t("th",{class:"px-6 py-3 text-left text-xs font-semibold text-gray-900 uppercase tracking-wider"},"Status")])],-1)),t("tbody",yt,[s.leads.qty_1?(a(),l("tr",ft,[e[21]||(e[21]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 1",-1)),t("td",pt,o(parseInt(s.leads.qty_1).toLocaleString())+" pcs",1),t("td",bt,[s.leads.confirmed_qty_1?(a(),l("span",wt,o(parseInt(s.leads.confirmed_qty_1).toLocaleString())+" pcs ",1)):(a(),l("span",ht,"Not confirmed"))]),t("td",vt,[s.leads.confirmed_qty_1?(a(),l("span",kt," \u2713 Confirmed ")):(a(),l("span",Ct," Pending "))])])):i("",!0),s.leads.qty_2?(a(),l("tr",St,[e[22]||(e[22]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 2",-1)),t("td",Lt,o(parseInt(s.leads.qty_2).toLocaleString())+" pcs",1),t("td",qt,[s.leads.confirmed_qty_2?(a(),l("span",_t,o(parseInt(s.leads.confirmed_qty_2).toLocaleString())+" pcs ",1)):(a(),l("span",Nt,"Not confirmed"))]),t("td",It,[s.leads.confirmed_qty_2?(a(),l("span",At," \u2713 Confirmed ")):(a(),l("span",Bt," Pending "))])])):i("",!0),s.leads.qty_3?(a(),l("tr",Mt,[e[23]||(e[23]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 3",-1)),t("td",Pt,o(parseInt(s.leads.qty_3).toLocaleString())+" pcs",1),t("td",$t,[s.leads.confirmed_qty_3?(a(),l("span",zt,o(parseInt(s.leads.confirmed_qty_3).toLocaleString())+" pcs ",1)):(a(),l("span",Dt,"Not confirmed"))]),t("td",jt,[s.leads.confirmed_qty_3?(a(),l("span",Qt," \u2713 Confirmed ")):(a(),l("span",Vt," Pending "))])])):i("",!0),s.leads.qty_4?(a(),l("tr",Et,[e[24]||(e[24]=t("td",{class:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900"},"Quantity 4",-1)),t("td",Ut,o(parseInt(s.leads.qty_4).toLocaleString())+" pcs",1),t("td",Ot,[s.leads.confirmed_qty_4?(a(),l("span",Ft,o(parseInt(s.leads.confirmed_qty_4).toLocaleString())+" pcs ",1)):(a(),l("span",Ht,"Not confirmed"))]),t("td",Rt,[s.leads.confirmed_qty_4?(a(),l("span",Tt," \u2713 Confirmed ")):(a(),l("span",Wt," Pending "))])])):i("",!0)])])]),s.leads.order_confirmed_at?(a(),l("div",Zt,[t("div",Gt,[e[27]||(e[27]=t("svg",{class:"w-5 h-5 text-green-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})],-1)),t("div",null,[e[26]||(e[26]=t("p",{class:"text-sm font-medium text-green-800"},"Order Confirmed",-1)),t("p",Jt,o(g(s.leads.order_confirmed_at)),1)])])])):i("",!0)]),s.leads.notes?(a(),l("div",Kt,[e[29]||(e[29]=t("h2",{class:"text-xl font-semibold text-gray-900 mb-4"},"Notes",-1)),t("p",Xt,o(s.leads.notes),1)])):i("",!0)]),t("div",Yt,[t("div",te,[e[32]||(e[32]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Quick Actions",-1)),t("div",ee,[m(S,{href:n.route("leads.edit",s.leads.id),class:"w-full"},{svg:u(()=>e[30]||(e[30]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),C(" Edit Lead ")],-1)])),_:1},8,["href"]),m(S,{href:n.route("leads.index"),class:"w-full"},{svg:u(()=>e[31]||(e[31]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),C(" Back to List ")],-1)])),_:1},8,["href"])])]),s.leads.documents&&s.leads.documents.length>0?(a(),l("div",se,[e[34]||(e[34]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Documents",-1)),t("div",le,[(a(!0),l(y,null,k(s.leads.documents,d=>(a(),l("div",{key:d.id,class:"flex items-center justify-between p-3 bg-gray-50 rounded-lg"},[t("div",ae,[e[33]||(e[33]=t("svg",{class:"w-5 h-5 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),t("span",oe,o(d.orignal_name),1)]),t("a",{href:"/uploads/leads/"+d.name,target:"_blank",class:"text-blue-600 hover:text-blue-800 text-sm"},"View",8,ne)]))),128))])])):i("",!0),t("div",de,[e[35]||(e[35]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Comments",-1)),m(Q,{comments:s.leads.comments||[],"current-user-id":n.$page.props.auth.user.id,"is-admin":n.$page.props.auth.user.role_id===1,onAddComment:e[4]||(e[4]=d=>A(n.lead))},null,8,["comments","current-user-id","is-admin"])]),t("div",ie,[e[40]||(e[40]=t("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"Timeline",-1)),t("div",re,[t("div",me,[e[37]||(e[37]=t("div",{class:"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"},null,-1)),t("div",null,[e[36]||(e[36]=t("p",{class:"text-sm font-semibold text-gray-900"},"leads Created",-1)),t("p",xe,o(g(s.leads.created_at)),1)])]),s.leads.updated_at!==s.leads.created_at?(a(),l("div",ce,[e[39]||(e[39]=t("div",{class:"flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"},null,-1)),t("div",null,[e[38]||(e[38]=t("p",{class:"text-sm font-semibold text-gray-900"},"Last Updated",-1)),t("p",ue,o(g(s.leads.updated_at)),1)])])):i("",!0)])])])])]),m(E,{show:x.value.show,onClose:f},{default:u(()=>[t("div",ge,[e[41]||(e[41]=t("h3",{class:"text-lg font-medium mb-4"},"Add Comment",-1)),m(V,{"lead-id":x.value.leadId,comments:x.value.comments,"current-user-id":n.$page.props.auth.user.id,"is-admin":n.$page.props.auth.user.role_id===1,onClose:f},null,8,["lead-id","comments","current-user-id","is-admin"])])]),_:1},8,["show"])]}),_:1})],64))}};export{ve as default};
