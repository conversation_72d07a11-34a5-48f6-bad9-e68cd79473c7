import{T as I,j as h,A as G,a as c,b as o,u as d,w as S,F as H,d as m,Z,e,y as K,t as r,f as y,x as J}from"./app.0e820f21.js";import{_ as R,a as W}from"./AdminLayout.687face1.js";import{_ as C,a as _}from"./TextInput.a134c4d6.js";import{_ as u}from"./InputLabel.c491b164.js";import{P as X}from"./PrimaryButton.259b896f.js";import{_ as Y}from"./TextArea.3742605b.js";import{_ as D}from"./Checkbox.a38f6303.js";import"./plugin-vue_export-helper.21dcd24c.js";const ee={class:"animate-top bg-white p-4 shadow sm:p-6 sm:rounded-lg border"},te={class:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"},le={class:"text-sm text-blue-800"},se={class:"mt-6 p-4 bg-gray-50 rounded-lg"},oe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ae={class:"text-sm text-gray-700"},de={class:"text-sm text-gray-700"},ne={class:"text-sm text-gray-700"},ie={class:"text-sm text-gray-700"},re={class:"text-sm text-gray-700"},ue={class:"text-sm text-gray-700"},ce={class:"text-sm text-gray-700"},me={class:"text-sm text-gray-700"},ye={key:0},_e={class:"text-sm text-gray-700"},qe={class:"border-b border-gray-900/10 pb-12"},xe={class:"mt-6 grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ge={key:0,class:"sm:col-span-3"},ve={class:"flex items-center space-x-3 mb-4 p-3 bg-blue-50 rounded-lg"},fe={class:"grid grid-cols-1 gap-4"},pe={class:"text-sm text-gray-500 mt-1"},be={key:1,class:"sm:col-span-3"},ke={class:"flex items-center space-x-3 mb-4 p-3 bg-green-50 rounded-lg"},he={class:"grid grid-cols-1 gap-4"},Ce={class:"text-sm text-gray-500 mt-1"},Ae={key:2,class:"sm:col-span-3"},Ve={class:"flex items-center space-x-3 mb-4 p-3 bg-yellow-50 rounded-lg"},we={class:"grid grid-cols-1 gap-4"},Ue={class:"text-sm text-gray-500 mt-1"},Ne={key:3,class:"sm:col-span-3"},$e={class:"flex items-center space-x-3 mb-4 p-3 bg-purple-50 rounded-lg"},Qe={class:"grid grid-cols-1 gap-4"},De={class:"text-sm text-gray-500 mt-1"},Fe={key:4,class:"sm:col-span-12"},Se={class:"bg-green-50 border border-green-200 p-6 rounded-lg"},Oe={class:"flex items-center justify-between"},Be={class:"text-lg font-semibold text-green-800"},Pe={class:"sm:col-span-6"},Me={class:"sm:col-span-12"},je={class:"flex mt-6 items-center justify-between"},ze={class:"ml-auto flex items-center justify-end gap-x-6"},Je={__name:"Add",props:{quotation:{type:Object,required:!0}},setup(s){var B,P,M,j;const i=s,l=I({quotation_id:i.quotation.id,lead_id:i.quotation.lead_id,selected_qty_1:((B=i.quotation.lead)==null?void 0:B.qty_1)||"",selected_qty_2:((P=i.quotation.lead)==null?void 0:P.qty_2)||"",selected_qty_3:((M=i.quotation.lead)==null?void 0:M.qty_3)||"",selected_qty_4:((j=i.quotation.lead)==null?void 0:j.qty_4)||"",notes:"",expected_delivery:"",tracking_number:""}),q=h(!1),x=h(!1),g=h(!1),v=h(!1),A=h(0),L=()=>{const n={quotation_id:l.quotation_id,lead_id:l.lead_id,notes:l.notes,expected_delivery:l.expected_delivery,tracking_number:l.tracking_number,selected_qty_1:null,selected_qty_2:null,selected_qty_3:null,selected_qty_4:null};return q.value&&l.selected_qty_1&&(n.selected_qty_1=l.selected_qty_1),x.value&&l.selected_qty_2&&(n.selected_qty_2=l.selected_qty_2),g.value&&l.selected_qty_3&&(n.selected_qty_3=l.selected_qty_3),v.value&&l.selected_qty_4&&(n.selected_qty_4=l.selected_qty_4),n},E=()=>{const n=L();I(n).post(route("orders.store"),{preserveScroll:!0,onSuccess:()=>l.reset()})},O=()=>{let n=0;q.value&&l.selected_qty_1&&i.quotation.price_qty_1&&(n+=parseFloat(l.selected_qty_1)*parseFloat(i.quotation.price_qty_1)),x.value&&l.selected_qty_2&&i.quotation.price_qty_2&&(n+=parseFloat(l.selected_qty_2)*parseFloat(i.quotation.price_qty_2)),g.value&&l.selected_qty_3&&i.quotation.price_qty_3&&(n+=parseFloat(l.selected_qty_3)*parseFloat(i.quotation.price_qty_3)),v.value&&l.selected_qty_4&&i.quotation.price_qty_4&&(n+=parseFloat(l.selected_qty_4)*parseFloat(i.quotation.price_qty_4)),A.value=n},V=(n,t)=>{O()};G([()=>l.selected_qty_1,()=>q.value,()=>l.selected_qty_2,()=>x.value,()=>l.selected_qty_3,()=>g.value,()=>l.selected_qty_4,()=>v.value],O);const F=new Date;F.setDate(F.getDate()+7),l.expected_delivery=F.toISOString().split("T")[0];const f=n=>{var b,k;const t={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},w=((k=(b=i.quotation.lead)==null?void 0:b.county)==null?void 0:k.name)||"UK",U=Object.keys(t).find(Q=>w.toLowerCase().includes(Q.toLowerCase())),{locale:N,currency:p}=t[U]||t.UK,$=new Intl.NumberFormat(N,{style:"currency",currency:p,currencyDisplay:"symbol"}).format(n);return`${p} ${$}`};return(n,t)=>(m(),c(H,null,[o(d(Z),{title:"Orders"}),o(R,null,{default:S(()=>{var w,U,N,p,$,b,k,Q,z,T;return[e("div",ee,[t[30]||(t[30]=e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"}," Convert Quotation to Order ",-1)),e("div",te,[e("p",le,[t[14]||(t[14]=e("strong",null,"Converting from Quotation:",-1)),K(" "+r(s.quotation.quotation_number),1)])]),t[31]||(t[31]=e("div",{class:"sm:col-span-12 mt-6"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Lead Information")],-1)),e("div",se,[e("div",oe,[e("div",null,[t[15]||(t[15]=e("p",{class:"text-sm font-semibold text-gray-900"},"Client Name",-1)),e("p",ae,r(((w=s.quotation.lead)==null?void 0:w.client_name)||"N/A"),1)]),e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm font-semibold text-gray-900"},"County",-1)),e("p",de,r(((N=(U=s.quotation.lead)==null?void 0:U.county)==null?void 0:N.name)||"N/A"),1)]),e("div",null,[t[17]||(t[17]=e("p",{class:"text-sm font-semibold text-gray-900"},"Dimensions",-1)),e("p",ne,r(((p=s.quotation.lead)==null?void 0:p.dimensions)||"N/A"),1)]),e("div",null,[t[18]||(t[18]=e("p",{class:"text-sm font-semibold text-gray-900"},"Open Size",-1)),e("p",ie,r((($=s.quotation.lead)==null?void 0:$.open_size)||"N/A"),1)]),e("div",null,[t[19]||(t[19]=e("p",{class:"text-sm font-semibold text-gray-900"},"Box Style",-1)),e("p",re,r(((b=s.quotation.lead)==null?void 0:b.box_style)||"N/A"),1)]),e("div",null,[t[20]||(t[20]=e("p",{class:"text-sm font-semibold text-gray-900"},"Stock",-1)),e("p",ue,r(((k=s.quotation.lead)==null?void 0:k.stock)||"N/A"),1)]),e("div",null,[t[21]||(t[21]=e("p",{class:"text-sm font-semibold text-gray-900"},"Lamination",-1)),e("p",ce,r(((Q=s.quotation.lead)==null?void 0:Q.lamination)||"N/A"),1)]),e("div",null,[t[22]||(t[22]=e("p",{class:"text-sm font-semibold text-gray-900"},"Printing",-1)),e("p",me,r(((z=s.quotation.lead)==null?void 0:z.printing)||"N/A"),1)]),(T=s.quotation.lead)!=null&&T.add_ons?(m(),c("div",ye,[t[23]||(t[23]=e("p",{class:"text-sm font-semibold text-gray-900"},"Add-ons",-1)),e("p",_e,r(s.quotation.lead.add_ons),1)])):y("",!0)])]),e("form",{onSubmit:J(E,["prevent"])},[e("div",qe,[e("div",xe,[t[26]||(t[26]=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4"},"Select Quantities for Order"),e("p",{class:"text-sm text-gray-600"},"Select the quantities that the client needs. Check the boxes and enter the required amounts:")],-1)),s.quotation.lead.qty_1&&s.quotation.price_qty_1?(m(),c("div",ge,[e("div",ve,[o(D,{checked:q.value,"onUpdate:checked":[t[0]||(t[0]=a=>q.value=a),t[1]||(t[1]=a=>V(1,a))]},null,8,["checked"]),o(u,{value:"Client wants Quantity 1",class:"text-base font-medium text-blue-800"})]),e("div",fe,[e("div",null,[o(u,{for:"selected_qty_1",value:`Client Order Qty 1 (Available: ${s.quotation.lead.qty_1})`},null,8,["value"]),o(C,{id:"selected_qty_1",type:"number",modelValue:d(l).selected_qty_1,"onUpdate:modelValue":t[2]||(t[2]=a=>d(l).selected_qty_1=a),max:s.quotation.lead.qty_1,min:"1",placeholder:`Max: ${s.quotation.lead.qty_1}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(_,{message:d(l).errors.selected_qty_1},null,8,["message"]),e("p",pe,"Price: "+r(f(s.quotation.price_qty_1))+" per unit",1)])])])):y("",!0),s.quotation.lead.qty_2&&s.quotation.price_qty_2?(m(),c("div",be,[e("div",ke,[o(D,{checked:x.value,"onUpdate:checked":[t[3]||(t[3]=a=>x.value=a),t[4]||(t[4]=a=>V(2,a))]},null,8,["checked"]),o(u,{value:"Client wants Quantity 2",class:"text-base font-medium text-green-800"})]),e("div",he,[e("div",null,[o(u,{for:"selected_qty_2",value:`Client Order Qty 2 (Available: ${s.quotation.lead.qty_2})`},null,8,["value"]),o(C,{id:"selected_qty_2",type:"number",modelValue:d(l).selected_qty_2,"onUpdate:modelValue":t[5]||(t[5]=a=>d(l).selected_qty_2=a),max:s.quotation.lead.qty_2,min:"1",placeholder:`Max: ${s.quotation.lead.qty_2}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(_,{message:d(l).errors.selected_qty_2},null,8,["message"]),e("p",Ce,"Price: "+r(f(s.quotation.price_qty_2))+" per unit",1)])])])):y("",!0),s.quotation.lead.qty_3&&s.quotation.price_qty_3?(m(),c("div",Ae,[e("div",Ve,[o(D,{checked:g.value,"onUpdate:checked":[t[6]||(t[6]=a=>g.value=a),t[7]||(t[7]=a=>V(3,a))]},null,8,["checked"]),o(u,{value:"Client wants Quantity 3",class:"text-base font-medium text-yellow-800"})]),e("div",we,[e("div",null,[o(u,{for:"selected_qty_3",value:`Client Order Qty 3 (Available: ${s.quotation.lead.qty_3})`},null,8,["value"]),o(C,{id:"selected_qty_3",type:"number",modelValue:d(l).selected_qty_3,"onUpdate:modelValue":t[8]||(t[8]=a=>d(l).selected_qty_3=a),max:s.quotation.lead.qty_3,min:"1",placeholder:`Max: ${s.quotation.lead.qty_3}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(_,{message:d(l).errors.selected_qty_3},null,8,["message"]),e("p",Ue,"Price: "+r(f(s.quotation.price_qty_3))+" per unit",1)])])])):y("",!0),s.quotation.lead.qty_4&&s.quotation.price_qty_4?(m(),c("div",Ne,[e("div",$e,[o(D,{checked:v.value,"onUpdate:checked":[t[9]||(t[9]=a=>v.value=a),t[10]||(t[10]=a=>V(4,a))]},null,8,["checked"]),o(u,{value:"Client wants Quantity 4",class:"text-base font-medium text-purple-800"})]),e("div",Qe,[e("div",null,[o(u,{for:"selected_qty_4",value:`Client Order Qty 4 (Available: ${s.quotation.lead.qty_4})`},null,8,["value"]),o(C,{id:"selected_qty_4",type:"number",modelValue:d(l).selected_qty_4,"onUpdate:modelValue":t[11]||(t[11]=a=>d(l).selected_qty_4=a),max:s.quotation.lead.qty_4,min:"1",placeholder:`Max: ${s.quotation.lead.qty_4}`,disabled:!0},null,8,["modelValue","max","placeholder"]),o(_,{message:d(l).errors.selected_qty_4},null,8,["message"]),e("p",De,"Price: "+r(f(s.quotation.price_qty_4))+" per unit",1)])])])):y("",!0),A.value>0?(m(),c("div",Fe,[e("div",Se,[e("div",Oe,[e("div",null,[e("p",Be," Order Total: "+r(f(A.value.toFixed(2))),1),t[24]||(t[24]=e("p",{class:"text-sm text-green-600 mt-1"}," Based on selected quantities and pricing from quotation ",-1))]),t[25]||(t[25]=e("div",{class:"text-right"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z","clip-rule":"evenodd"})])],-1))])])])):y("",!0),t[27]||(t[27]=e("div",{class:"sm:col-span-12"},[e("h3",{class:"text-lg font-semibold text-gray-900 mb-4 mb-4 mb-4"},"Order Information")],-1)),e("div",Pe,[o(u,{for:"expected_delivery",value:"Expected Delivery Date"}),o(C,{id:"expected_delivery",type:"date",modelValue:d(l).expected_delivery,"onUpdate:modelValue":t[12]||(t[12]=a=>d(l).expected_delivery=a)},null,8,["modelValue"]),o(_,{message:d(l).errors.expected_delivery},null,8,["message"])]),e("div",Me,[o(u,{for:"notes",value:"Order Notes"}),o(Y,{id:"notes",modelValue:d(l).notes,"onUpdate:modelValue":t[13]||(t[13]=a=>d(l).notes=a),rows:"4",placeholder:"Any special instructions or notes for this order..."},null,8,["modelValue"]),o(_,{message:d(l).errors.notes},null,8,["message"])])])]),e("div",je,[e("div",ze,[o(W,{href:n.route("quotations.show",s.quotation.id)},{svg:S(()=>t[28]||(t[28]=[e("button",{type:"button",class:"text-sm font-semibold leading-6 text-gray-900"},"Cancel",-1)])),_:1},8,["href"]),o(X,{disabled:d(l).processing||A.value===0},{default:S(()=>t[29]||(t[29]=[K(" Save ")])),_:1,__:[29]},8,["disabled"])])])],32)])]}),_:1})],64))}};export{Je as default};
