
<script setup>
import { ref, computed } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import AdminLayout from '@/Layouts/AdminLayout.vue'
import Modal from '@/Components/Modal.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import CreateButton from '@/Components/CreateButton.vue';
import ActionLink from '@/Components/ActionLink.vue';
import axios from 'axios';
const props = defineProps({
    task: Object
})

const formatDateTime = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
};

const formatType = (type) => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}

const isOverdue = (dueDate) => {
    return new Date(dueDate) < new Date() && props.task.status !== 'completed'
}

const getTypeColor = (type) => {
    const colors = {
        call: 'bg-blue-100 text-blue-800',
        follow_up: 'bg-yellow-100 text-yellow-800',
        meeting: 'bg-purple-100 text-purple-800',
        email: 'bg-green-100 text-green-800',
        quote_follow_up: 'bg-orange-100 text-orange-800',
        order_follow_up: 'bg-red-100 text-red-800',
        general: 'bg-gray-100 text-gray-800',
        reminder: 'bg-pink-100 text-pink-800'
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
}

const getPriorityColor = (priority) => {
    const colors = {
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800'
    }
    return colors[priority] || 'bg-gray-100 text-gray-800'
}

const getStatusColor = (status) => {
    const colors = {
        pending: 'bg-yellow-100 text-yellow-800',
        in_progress: 'bg-blue-100 text-blue-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
}



const modalVisible = ref(false);
const selectedTaskId = ref(null);

const oncompleteTask = (taskId) => {
    selectedTaskId.value = taskId;
    modalVisible.value = true;
};

const closeModal = () => {
    modalVisible.value = false;
};

const completeTask = () => {
    const taskId = selectedTaskId.value;
    const url = route('tasks.complete', selectedTaskId.value);
    axios.post(url)
    .then(response => {
        console.log("Task completed successfully", response.data.message);
        modalVisible.value = false;
        router.reload();  // Reload page after success
    })
    .catch(error => {
        console.error("Error completing task:", error.response?.data?.error || error.message);
    });
};

</script>
<template>
    <Head title="Tasks" />

    <AdminLayout>
        <div class="animate-top">
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-semibold leading-7 text-gray-900">{{ task.title }}</h2>
                    <p class="text-sm text-gray-600 mt-1">Task Details</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Task Details -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Task Info Card -->
                    <div class="bg-white border rounded-lg p-6  bg-white p-4 shadowrounded-lg border">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Task Information</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Type</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm  font-semibold rounded-full"
                                          :class="getTypeColor(task.type)">
                                        {{ formatType(task.type) }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Priority</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                                          :class="getPriorityColor(task.priority)">
                                        {{ task.priority.toUpperCase() }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Status</label>
                                <div class="mt-1">
                                    <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full"
                                          :class="getStatusColor(task.status)">
                                        {{ formatStatus(task.status) }}
                                    </span>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Assigned To</label>
                                <div class="mt-1 text-sm text-gray-700">
                                    {{ task.assigned_to.first_name }} {{ task.assigned_to.last_name }}
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Due Date</label>
                                <div class="mt-1 text-sm"
                                     :class="{ 'text-red-600 font-semibold': isOverdue(task.due_date), 'text-gray-900': !isOverdue(task.due_date) }">
                                    {{ formatDateTime(task.due_date) }}
                                    <span v-if="isOverdue(task.due_date)" class="ml-2 text-red-500">⚠️ Overdue</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-gray-900">Created By</label>
                                <div class="mt-1 text-sm text-gray-700">
                                    {{ task.created_by.first_name }} {{ task.created_by.last_name }}
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div v-if="task.description" class="mt-6">
                            <label class="block text-sm font-semibold text-gray-900 mb-2">Description</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ task.description }}</p>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div v-if="task.notes" class="mt-6">
                            <label class="block text-sm font-semibold text-gray-900 mb-2">Notes</label>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ task.notes }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Related Lead -->
                    <div v-if="task.lead" class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Lead</h3>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-blue-900">
                                        📋 Lead: {{ task.lead.client_name }}
                                    </div>
                                    <div class="text-sm text-blue-700">
                                        <span v-if="task.lead.county">{{ task.lead.county.name }}</span>
                                        <span v-if="task.lead.number">📞 {{ task.lead.number }}</span>
                                        <span v-if="task.lead.email">📧 {{ task.lead.email }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Activity Log -->
                    <div v-if="task.activity_logs && task.activity_logs.length > 0" class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Activity History</h3>
                        <div class="space-y-4">
                            <div v-for="activity in task.activity_logs" :key="activity.id"
                                 class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs">{{ activity.user.first_name.charAt(0) }}</span>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ activity.user.first_name }} {{ activity.user.last_name }}
                                    </div>
                                    <div class="text-sm text-gray-600">{{ activity.description }}</div>
                                    <div class="text-xs text-gray-500 mt-1">
                                        {{ formatDateTime(activity.created_at) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Quick Actions -->
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="space-y-3">
                            <button v-if="task.status !== 'completed'" @click="oncompleteTask(task.id)"
                                    class="w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700">
                                ✅ Mark as Complete
                            </button>
                            <ActionLink :href="route('tasks.edit', task.id)" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" />
                                        </svg>
                                        Edit Task
                                    </button>
                                </template>
                            </ActionLink>
                            <ActionLink :href="route('tasks.index')" class="w-full">
                                <template #svg>
                                    <button class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                                        </svg>
                                        Back to List
                                    </button>
                                </template>
                            </ActionLink>
                        </div>
                    </div>

                    <!-- Task Timeline -->
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
                        <div class="space-y-3">
                            <div class="flex items-center text-sm">
                                <div class="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium">Created</div>
                                    <div class="text-gray-700">{{ formatDateTime(task.created_at) }}</div>
                                </div>
                            </div>
                            <div class="flex items-center text-sm">
                                <div class="w-2 h-2 rounded-full mr-3"
                                     :class="{ 'bg-red-500': isOverdue(task.due_date), 'bg-yellow-500': !isOverdue(task.due_date) }"></div>
                                <div>
                                    <div class="font-medium">Due Date</div>
                                    <div :class="{ 'text-red-500': isOverdue(task.due_date), 'text-gray-700': !isOverdue(task.due_date) }">
                                        {{ formatDateTime(task.due_date) }}
                                    </div>
                                </div>
                            </div>
                            <div v-if="task.completed_at" class="flex items-center text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                                <div>
                                    <div class="font-medium">Completed</div>
                                    <div class="text-gray-700">{{ formatDateTime(task.completed_at) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <Modal :show="modalVisible" @close="closeModal">
            <div class="p-6">
                <h2 class="text-lg font-medium text-gray-900">
                    Are you sure you want to complete this task?
                </h2>
                <div class="mt-6 flex justify-end space-x-4">
                    <SecondaryButton @click="closeModal">Cancel</SecondaryButton>
                    <div class="w-44">
                        <CreateButton @click="completeTask">
                            Complete Task
                        </CreateButton>
                    </div>
                </div>
            </div>
        </Modal>
    </AdminLayout>
</template>

