import{j as y,c as j,a as c,b as o,u as w,w as n,F as B,d as a,Z as se,e,l as H,B as le,K as oe,f as b,r as U,p as A,y as V,t as p,v as ae,g as ne}from"./app.0e820f21.js";import{_ as re,b as ie,a as O}from"./AdminLayout.687face1.js";import{_ as de}from"./SecondaryButton.f2b207b7.js";import{D as ue}from"./DangerButton.7efeecc5.js";import{M as ce}from"./Modal.c671de5e.js";import{s as me,_ as pe}from"./sortAndSearch.29e714e8.js";import{_ as z}from"./SearchableDropdownNew.0cffdca8.js";import{_ as M}from"./InputLabel.c491b164.js";import{_ as ge}from"./ArrowIcon.f2c2b1ba.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const ve={class:"animate-top"},fe={class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0"},ye={class:"flex flex-col sm:flex-row sm:items-center sm:space-x-6 space-y-4 sm:space-y-0 w-full sm:w-auto"},we={class:"flex focus-within:shadow-lg relative rounded-lg border border-gray-200 w-full sm:w-64"},be={class:"mt-8 p-6 bg-white shadow ring-1 ring-black ring-opacity-5 rounded-lg"},he={class:"flex justify-between mb-2"},xe={class:"flex"},_e={class:"grid grid-cols-1 gap-x-6 gap-y-5 sm:grid-cols-12"},ke={key:0,class:"sm:col-span-4"},Ce={class:"relative mt-2"},Ae={class:"sm:col-span-4"},Ve={class:"relative mt-2"},Me={class:"sm:col-span-4"},Ne={class:"relative mt-2"},$e={class:"mt-8 overflow-x-auto rounded-lg max-w-full"},Se={class:"shadow rounded-lg"},je={class:"w-full text-sm text-left rtl:text-right text-gray-500"},Be={class:"text-xs text-gray-700 uppercase bg-gray-50",style:{"border-top":"3px solid white"}},Oe={class:"border-b-2"},ze=["onClick"],De={key:0},Qe={class:"px-4 py-2.5 min-w-36"},Le={class:"px-4 py-2.5 min-w-28"},Te={class:"px-4 py-2.5 font-medium text-gray-900 whitespace-nowrap min-w-36"},He={class:"px-4 py-2.5"},Ue={class:"px-4 py-2.5 whitespace-nowrap"},Ee={class:"px-4 py-2.5"},Fe={class:"flex items-center space-x-2 min-w-36"},Ie={key:0,class:"px-4 py-2.5"},Pe={class:"items-center px-4 py-2.5"},We={class:"flex items-center justify-start gap-4"},Ke=["onClick"],Re=["onClick"],Ye={key:1},Ge={class:"p-6"},Ze={class:"mt-6 flex justify-end"},dt={__name:"List",props:["data","search","permissions","counties","county_id","agents","agent_id","statusOptions","status","isAdmin"],setup(r){const d=r,{form:N,search:h,sort:E,fetchData:Je,sortKey:F,sortDirection:I}=me("quotations.index"),$=y(!1),D=y(null),P=j(()=>[{id:"",name:"All Agents"},...d.agents]),W=j(()=>[{id:"",name:"All Country"},...d.counties]),K=j(()=>[{id:"",name:"All Status"},...d.statusOptions]),R=[{field:"quotation_number",label:"QUOTATION NO",sortable:!0,visible:!0},{field:"lead.lead_number",label:"LEAD NO",sortable:!0,visible:!0},{field:"lead.client_name",label:"CLIENT NAME",sortable:!0,visible:!0},{field:"county.name",label:"COUNTRY",sortable:!0,visible:!0},{field:"qty",label:"QTY",sortable:!1,visible:!0},{field:"status",label:"STATUS",sortable:!0,visible:!0},{field:"creator.first_name",label:"AGENT",sortable:!0,visible:d.isAdmin},{field:"action",label:"ACTION",sortable:!1,visible:!0}],Y=s=>{D.value=s,$.value=!0},S=()=>{$.value=!1},G=()=>{N.delete(route("quotations.destroy",{quotation:D.value}),{onSuccess:()=>S()})},Z=s=>({pending:"bg-blue-100 text-blue-800",quotation_ready:"bg-yellow-100 text-yellow-800",order_placed:"bg-green-100 text-green-800"})[s]||"bg-gray-100 text-gray-800",g=y(d.agent_id||""),v=y(d.county_id||""),f=y(d.status||""),x=y("");y({});const J=(s,t)=>{g.value=s,_(x.value,g.value,v.value,f.value)},X=(s,t)=>{v.value=s,_(x.value,g.value,v.value,f.value)},q=(s,t)=>{f.value=s,_(x.value,g.value,v.value,f.value)},_=(s,t,l,i)=>{x.value=s;const u=t===""?null:t,k=l===""?null:l,C=i===""?null:i;N.get(route("quotations.index",{search:s,agent_id:u,county_id:k,status:C}),{preserveState:!0})},ee=s=>{window.open(route("quotations.pdf",s),"_blank")};function te(s){var l,i,u,k,C,Q,L,T;return[{label:"1",qty:(l=s==null?void 0:s.qty_1)!=null?l:null,price:(i=s==null?void 0:s.price_qty_1)!=null?i:null},{label:"2",qty:(u=s==null?void 0:s.qty_2)!=null?u:null,price:(k=s==null?void 0:s.price_qty_2)!=null?k:null},{label:"3",qty:(C=s==null?void 0:s.qty_3)!=null?C:null,price:(Q=s==null?void 0:s.price_qty_3)!=null?Q:null},{label:"4",qty:(L=s==null?void 0:s.qty_4)!=null?L:null,price:(T=s==null?void 0:s.price_qty_4)!=null?T:null}].filter(m=>m.qty!=null).map(m=>m.price!=null?`Qty ${m.label}: ${m.qty} - \xA3 ${m.price}`:`Qty ${m.label}: ${m.qty}`).join(", ")||"N/A"}return(s,t)=>(a(),c(B,null,[o(w(se),{title:"Quotations"}),o(re,null,{default:n(()=>[e("div",ve,[e("div",fe,[t[6]||(t[6]=e("div",{class:"items-start"},[e("h1",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Quotations")],-1)),e("div",ye,[e("div",we,[t[5]||(t[5]=e("svg",{class:"pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400 ml-2",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[e("path",{"fill-rule":"evenodd",d:"M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z","clip-rule":"evenodd"})],-1)),H(e("input",{type:"text","onUpdate:modelValue":t[0]||(t[0]=l=>oe(h)?h.value=l:null),onInput:t[1]||(t[1]=l=>_(w(h),g.value,v.value,f.value)),class:"block p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg w-full bg-white",placeholder:"Search for quotations..."},null,544),[[le,w(h)]])])])]),e("div",be,[e("div",he,[e("div",xe,[t[7]||(t[7]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 6h18M4 10h16M5 14h14M6 18h12"})],-1)),o(M,{for:"filters",value:"Filters"})])]),e("div",_e,[d.isAdmin?(a(),c("div",ke,[o(M,{for:"agent_filter",value:"Agents"}),e("div",Ce,[o(z,{options:P.value,modelValue:g.value,"onUpdate:modelValue":t[2]||(t[2]=l=>g.value=l),onOnchange:J},null,8,["options","modelValue"])])])):b("",!0),e("div",Ae,[o(M,{for:"county_filter",value:"Country"}),e("div",Ve,[o(z,{options:W.value,modelValue:v.value,"onUpdate:modelValue":t[3]||(t[3]=l=>v.value=l),onOnchange:X},null,8,["options","modelValue"])])]),e("div",Me,[o(M,{for:"status_filter",value:"Status"}),e("div",Ne,[o(z,{options:K.value,modelValue:f.value,"onUpdate:modelValue":t[4]||(t[4]=l=>f.value=l),onOnchange:q},null,8,["options","modelValue"])])])])]),e("div",$e,[e("div",Se,[e("table",je,[e("thead",Be,[e("tr",Oe,[(a(),c(B,null,U(R,(l,i)=>H(e("th",{key:i,scope:"col",class:"px-4 py-4 text-sm font-semi bold text-gray-900 cursor-pointer",onClick:u=>w(E)(l.field,l.sortable)},[V(p(l.label)+" ",1),l.sortable?(a(),A(ge,{key:0,isSorted:w(F)===l.field,direction:w(I)},null,8,["isSorted","direction"])):b("",!0)],8,ze),[[ae,l.visible]])),64))])]),r.data.data&&r.data.data.length>0?(a(),c("tbody",De,[(a(!0),c(B,null,U(r.data.data,l=>{var i;return a(),c("tr",{class:"odd:bg-white even:bg-gray-50 border-b",key:l.id},[e("td",Qe,p(l.quotation_number),1),e("td",Le,p(l.lead?l.lead.lead_number:"N/A"),1),e("td",Te,p((i=l.lead)==null?void 0:i.client_name),1),e("td",He,p(l.county?l.county.name:"N/A"),1),e("td",Ue,p(te(l.lead)),1),e("td",Ee,[e("div",Fe,[e("span",{class:ne(["px-3 py-1 rounded-full text-sm font-medium cursor-pointer",Z(l.status)])},p(l.status.replace(/_/g," ").replace(/\b\w/g,u=>u.toUpperCase())),3)])]),d.isAdmin?(a(),c("td",Ie,p(l.lead.creator?l.lead.creator.first_name:"N/A"),1)):b("",!0),e("td",Pe,[e("div",We,[o(ie,{align:"right",width:"48"},{trigger:n(()=>t[8]||(t[8]=[e("button",{type:"button",title:"Open details",class:"p-1 rounded hover:bg-gray-100 focus:bg-gray-100"},[e("svg",{viewBox:"0 0 24 24",class:"w-4 h-4 fill-current"},[e("path",{d:"M12 6a2 2 0 110-4 2 2 0 010 4zm0 8a2 2 0 110-4 2 2 0 010 4zm-2 6a2 2 0 104 0 2 2 0 00-4 0z"})])],-1)])),content:n(()=>[o(O,{href:s.route("quotations.show",{quotation:l.id})},{svg:n(()=>t[9]||(t[9]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1)])),text:n(()=>t[10]||(t[10]=[e("span",{class:"text-sm text-gray-700 leading-5"},"View",-1)])),_:2},1032,["href"]),r.permissions.canEditQuotation&&l.status!=="order_placed"?(a(),A(O,{key:0,href:s.route("quotations.edit",{quotation:l.id})},{svg:n(()=>t[11]||(t[11]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})],-1)])),text:n(()=>t[12]||(t[12]=[e("span",{class:"text-sm text-gray-700 leading-5"},"Edit",-1)])),_:2},1032,["href"])):b("",!0),r.permissions.canDeleteQuotation&&l.status!=="order_placed"?(a(),c("button",{key:1,type:"button",onClick:u=>Y(l.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},t[13]||(t[13]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),e("span",{class:"text-sm text-gray-700 leading-5"},"Delete",-1)]),8,Ke)):b("",!0),e("button",{type:"button",onClick:u=>ee(l.id),class:"flex space-x-2 block px-3 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 w-full"},t[14]||(t[14]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-5 w-5"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3"})],-1),e("span",{class:"text-sm text-gray-700 leading-5"},"Download PDF",-1)]),8,Re),l.status==="quotation_ready"&&r.permissions.canConvertQuotation?(a(),A(O,{key:2,href:s.route("orders.convert",{quotation:l.id}),class:"w-full"},{svg:n(()=>t[15]||(t[15]=[e("button",{class:"w-full flex items-center justify-center px-4 py-2 bg-green-600 border border-green-600 rounded-md shadow-sm text-sm font-medium text-white hover:bg-green-700"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"})]),V(" Convert to Order ")],-1)])),_:2},1032,["href"])):b("",!0)]),_:2},1024)])])])}),128))])):(a(),c("tbody",Ye,t[16]||(t[16]=[e("tr",{class:"bg-white"},[e("td",{colspan:"9",class:"text-center whitespace-nowrap p-10 text-sm font-semibold text-gray-900"}," No data found. ")],-1)])))])])]),r.data.data&&r.data.data.length>0?(a(),A(pe,{key:0,class:"mt-6",links:r.data.links},null,8,["links"])):b("",!0)]),o(ce,{show:$.value,onClose:S},{default:n(()=>[e("div",Ge,[t[19]||(t[19]=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to delete this quotation? ",-1)),e("div",Ze,[o(de,{onClick:S},{default:n(()=>t[17]||(t[17]=[V("Cancel")])),_:1,__:[17]}),o(ue,{class:"ml-3",onClick:G,disabled:w(N).processing},{default:n(()=>t[18]||(t[18]=[V(" Delete Quotation ")])),_:1,__:[18]},8,["disabled"])])])]),_:1},8,["show"])]),_:1})],64))}};export{dt as default};
