import{j as v,a,b as m,u as N,w as u,F as w,d,Z as z,e as t,t as l,g,y as x,f as r,r as A,z as V,N as E}from"./app.0e820f21.js";import{_ as F,a as h}from"./AdminLayout.687face1.js";import{M as O}from"./Modal.c671de5e.js";import{_ as U}from"./SecondaryButton.f2b207b7.js";import{_ as H}from"./CreateButton.f13c50e2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const I={class:"animate-top"},P={class:"flex justify-between items-center mb-6"},q={class:"text-2xl font-semibold leading-7 text-gray-900"},Q={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},R={class:"lg:col-span-2 space-y-6"},Y={class:"bg-white border rounded-lg p-6 bg-white p-4 shadowrounded-lg border"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},G={class:"mt-1"},J={class:"mt-1"},K={class:"mt-1"},W={class:"mt-1 text-sm text-gray-700"},X={key:0,class:"ml-2 text-red-500"},tt={class:"mt-1 text-sm text-gray-700"},et={key:0,class:"mt-6"},st={class:"bg-gray-50 rounded-lg p-4"},lt={class:"text-sm text-gray-700 whitespace-pre-wrap"},ot={key:1,class:"mt-6"},at={class:"bg-gray-50 rounded-lg p-4"},dt={class:"text-sm text-gray-700 whitespace-pre-wrap"},nt={key:0,class:"bg-white border rounded-lg p-6"},rt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},it={class:"flex items-center"},mt={class:"ml-4"},ut={class:"text-sm font-medium text-blue-900"},gt={class:"text-sm text-blue-700"},ct={key:0},xt={key:1},bt={key:2},ft={key:1,class:"bg-white border rounded-lg p-6"},kt={class:"space-y-4"},yt={class:"flex-shrink-0"},vt={class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},wt={class:"text-xs"},ht={class:"flex-1"},pt={class:"text-sm font-medium text-gray-900"},_t={class:"text-sm text-gray-600"},Ct={class:"text-xs text-gray-500 mt-1"},Tt={class:"space-y-6"},St={class:"bg-white border rounded-lg p-6"},Dt={class:"space-y-3"},Mt={class:"bg-white border rounded-lg p-6"},jt={class:"space-y-3"},$t={class:"flex items-center text-sm"},Bt={class:"text-gray-700"},Lt={class:"flex items-center text-sm"},Nt={key:0,class:"flex items-center text-sm"},zt={class:"text-gray-700"},At={class:"p-6"},Vt={class:"mt-6 flex justify-end space-x-4"},Et={class:"w-44"},Qt={__name:"Show",props:{task:Object},setup(s){const p=s,c=o=>{const e=new Date(o),n=String(e.getDate()).padStart(2,"0"),f=String(e.getMonth()+1).padStart(2,"0"),$=e.getFullYear(),B=String(e.getHours()).padStart(2,"0"),L=String(e.getMinutes()).padStart(2,"0");return`${n}/${f}/${$} ${B}:${L}`},_=o=>o.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),C=o=>o.replace("_"," ").replace(/\b\w/g,e=>e.toUpperCase()),i=o=>new Date(o)<new Date&&p.task.status!=="completed",T=o=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[o]||"bg-gray-100 text-gray-800",S=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",D=o=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",b=v(!1),k=v(null),M=o=>{k.value=o,b.value=!0},y=()=>{b.value=!1},j=()=>{k.value;const o=route("tasks.complete",k.value);V.post(o).then(e=>{console.log("Task completed successfully",e.data.message),b.value=!1,E.reload()}).catch(e=>{var n,f;console.error("Error completing task:",((f=(n=e.response)==null?void 0:n.data)==null?void 0:f.error)||e.message)})};return(o,e)=>(d(),a(w,null,[m(N(z),{title:"Tasks"}),m(F,null,{default:u(()=>[t("div",I,[t("div",P,[t("div",null,[t("h2",q,l(s.task.title),1),e[1]||(e[1]=t("p",{class:"text-sm text-gray-600 mt-1"},"Task Details",-1))])]),t("div",Q,[t("div",R,[t("div",Y,[e[10]||(e[10]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Task Information",-1)),t("div",Z,[t("div",null,[e[2]||(e[2]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Type",-1)),t("div",G,[t("span",{class:g(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",T(s.task.type)])},l(_(s.task.type)),3)])]),t("div",null,[e[3]||(e[3]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Priority",-1)),t("div",J,[t("span",{class:g(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",S(s.task.priority)])},l(s.task.priority.toUpperCase()),3)])]),t("div",null,[e[4]||(e[4]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Status",-1)),t("div",K,[t("span",{class:g(["inline-flex px-3 py-1 text-sm font-semibold rounded-full",D(s.task.status)])},l(C(s.task.status)),3)])]),t("div",null,[e[5]||(e[5]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Assigned To",-1)),t("div",W,l(s.task.assigned_to.first_name)+" "+l(s.task.assigned_to.last_name),1)]),t("div",null,[e[6]||(e[6]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Due Date",-1)),t("div",{class:g(["mt-1 text-sm",{"text-red-600 font-semibold":i(s.task.due_date),"text-gray-900":!i(s.task.due_date)}])},[x(l(c(s.task.due_date))+" ",1),i(s.task.due_date)?(d(),a("span",X,"\u26A0\uFE0F Overdue")):r("",!0)],2)]),t("div",null,[e[7]||(e[7]=t("label",{class:"block text-sm font-semibold text-gray-900"},"Created By",-1)),t("div",tt,l(s.task.created_by.first_name)+" "+l(s.task.created_by.last_name),1)])]),s.task.description?(d(),a("div",et,[e[8]||(e[8]=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Description",-1)),t("div",st,[t("p",lt,l(s.task.description),1)])])):r("",!0),s.task.notes?(d(),a("div",ot,[e[9]||(e[9]=t("label",{class:"block text-sm font-semibold text-gray-900 mb-2"},"Notes",-1)),t("div",at,[t("p",dt,l(s.task.notes),1)])])):r("",!0)]),s.task.lead?(d(),a("div",nt,[e[12]||(e[12]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Related Lead",-1)),t("div",rt,[t("div",it,[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),t("div",mt,[t("div",ut," \u{1F4CB} Lead: "+l(s.task.lead.client_name),1),t("div",gt,[s.task.lead.county?(d(),a("span",ct,l(s.task.lead.county.name),1)):r("",!0),s.task.lead.number?(d(),a("span",xt,"\u{1F4DE} "+l(s.task.lead.number),1)):r("",!0),s.task.lead.email?(d(),a("span",bt,"\u{1F4E7} "+l(s.task.lead.email),1)):r("",!0)])])])])])):r("",!0),s.task.activity_logs&&s.task.activity_logs.length>0?(d(),a("div",ft,[e[13]||(e[13]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Activity History",-1)),t("div",kt,[(d(!0),a(w,null,A(s.task.activity_logs,n=>(d(),a("div",{key:n.id,class:"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"},[t("div",yt,[t("div",vt,[t("span",wt,l(n.user.first_name.charAt(0)),1)])]),t("div",ht,[t("div",pt,l(n.user.first_name)+" "+l(n.user.last_name),1),t("div",_t,l(n.description),1),t("div",Ct,l(c(n.created_at)),1)])]))),128))])])):r("",!0)]),t("div",Tt,[t("div",St,[e[16]||(e[16]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Quick Actions",-1)),t("div",Dt,[s.task.status!=="completed"?(d(),a("button",{key:0,onClick:e[0]||(e[0]=n=>M(s.task.id)),class:"w-full px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700"}," \u2705 Mark as Complete ")):r("",!0),m(h,{href:o.route("tasks.edit",s.task.id),class:"w-full"},{svg:u(()=>e[14]||(e[14]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125"})]),x(" Edit Task ")],-1)])),_:1},8,["href"]),m(h,{href:o.route("tasks.index"),class:"w-full"},{svg:u(()=>e[15]||(e[15]=[t("button",{class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[t("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-4 h-4 mr-2"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3"})]),x(" Back to List ")],-1)])),_:1},8,["href"])])]),t("div",Mt,[e[22]||(e[22]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Timeline",-1)),t("div",jt,[t("div",$t,[e[18]||(e[18]=t("div",{class:"w-2 h-2 bg-blue-500 rounded-full mr-3"},null,-1)),t("div",null,[e[17]||(e[17]=t("div",{class:"font-medium"},"Created",-1)),t("div",Bt,l(c(s.task.created_at)),1)])]),t("div",Lt,[t("div",{class:g(["w-2 h-2 rounded-full mr-3",{"bg-red-500":i(s.task.due_date),"bg-yellow-500":!i(s.task.due_date)}])},null,2),t("div",null,[e[19]||(e[19]=t("div",{class:"font-medium"},"Due Date",-1)),t("div",{class:g({"text-red-500":i(s.task.due_date),"text-gray-700":!i(s.task.due_date)})},l(c(s.task.due_date)),3)])]),s.task.completed_at?(d(),a("div",Nt,[e[21]||(e[21]=t("div",{class:"w-2 h-2 bg-green-500 rounded-full mr-3"},null,-1)),t("div",null,[e[20]||(e[20]=t("div",{class:"font-medium"},"Completed",-1)),t("div",zt,l(c(s.task.completed_at)),1)])])):r("",!0)])])])])]),m(O,{show:b.value,onClose:y},{default:u(()=>[t("div",At,[e[25]||(e[25]=t("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1)),t("div",Vt,[m(U,{onClick:y},{default:u(()=>e[23]||(e[23]=[x("Cancel")])),_:1,__:[23]}),t("div",Et,[m(H,{onClick:j},{default:u(()=>e[24]||(e[24]=[x(" Complete Task ")])),_:1,__:[24]})])])])]),_:1},8,["show"])]),_:1})],64))}};export{Qt as default};
