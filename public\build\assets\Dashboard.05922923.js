import{_ as S}from"./AdminLayout.687face1.js";import{T as C,o as k,c as j,a,b as v,u as A,w as M,F as m,d as r,Z as T,e as t,t as l,n as x,r as f,f as O,g as z}from"./app.0e820f21.js";import"./plugin-vue_export-helper.21dcd24c.js";const U={class:"animate-top"},R={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},q={class:"bg-white overflow-hidden shadow rounded-lg"},D={class:"p-5"},V={class:"flex items-center"},B={class:"ml-5 w-0 flex-1"},L={class:"text-lg font-semibold text-gray-900"},H={class:"bg-white overflow-hidden shadow rounded-lg"},K={class:"p-5"},F={class:"flex items-center"},P={class:"ml-5 w-0 flex-1"},Q={class:"text-lg font-semibold text-gray-900"},$={class:"bg-white overflow-hidden shadow rounded-lg"},I={class:"p-5"},G={class:"flex items-center"},N={class:"ml-5 w-0 flex-1"},E={class:"text-lg font-semibold text-gray-900"},J={class:"bg-white overflow-hidden shadow rounded-lg"},W={class:"p-5"},Z={class:"flex items-center"},X={class:"ml-5 w-0 flex-1"},Y={class:"text-lg font-semibold text-gray-900"},tt={class:"bg-white shadow rounded-lg p-6 mb-8"},st={class:"mb-6"},et={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},lt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},ot={class:"flex items-center"},dt={class:"ml-3"},nt={class:"text-lg font-bold text-blue-900"},it={class:"bg-green-50 border border-green-200 rounded-lg p-4"},at={class:"flex items-center"},rt={class:"ml-3"},ct={class:"text-lg font-bold text-green-900"},ut={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},mt={class:"flex items-center"},xt={class:"ml-3"},gt={class:"text-lg font-bold text-purple-900"},vt={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},ft={class:"flex items-center"},ht={class:"ml-3"},bt={class:"text-lg font-bold text-yellow-900"},yt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},wt={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},pt={class:"flex items-center"},_t={class:"ml-3"},St={class:"text-lg font-bold text-blue-900"},Ct={class:"bg-green-50 border border-green-200 rounded-lg p-4"},kt={class:"flex items-center"},jt={class:"ml-3"},At={class:"text-lg font-bold text-green-900"},Mt={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},Tt={class:"flex items-center"},Ot={class:"ml-3"},zt={class:"text-lg font-bold text-purple-900"},Ut={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Rt={class:"flex items-center"},qt={class:"ml-3"},Dt={class:"text-lg font-bold text-yellow-900"},Vt={class:"bg-white shadow rounded-lg p-6 mb-8"},Bt={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Lt={class:"text-center"},Ht={class:"text-3xl font-bold text-blue-600"},Kt={class:"text-center"},Ft={class:"text-3xl font-bold text-green-600"},Pt={class:"text-center"},Qt={class:"text-3xl font-bold text-purple-600"},$t={class:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8"},It={class:"bg-white shadow rounded-lg p-6"},Gt={class:"space-y-4"},Nt={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},Et={class:"w-full bg-gray-200 rounded-full h-2"},Jt={class:"flex justify-between text-xs text-gray-500 mt-1"},Wt={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},Zt={class:"w-full bg-gray-200 rounded-full h-2"},Xt={class:"flex justify-between text-xs text-gray-500 mt-1"},Yt={class:"flex justify-between text-sm font-medium text-gray-900 mb-1"},ts={class:"w-full bg-gray-200 rounded-full h-2"},ss={class:"flex justify-between text-xs text-gray-500 mt-1"},es={class:"bg-white shadow rounded-lg p-6"},ls={class:"space-y-3"},os={class:"flex items-center justify-between"},ds={class:"text-sm font-medium text-gray-900"},ns={class:"flex items-center justify-between"},is={class:"text-sm font-medium text-gray-900"},as={class:"flex items-center justify-between"},rs={class:"text-sm font-medium text-gray-900"},cs={class:"flex items-center justify-between"},us={class:"text-sm font-medium text-gray-900"},ms={class:"grid grid-cols-1 lg:grid-cols-3 gap-8"},xs={class:"lg:col-span-2 bg-white shadow rounded-lg p-6"},gs={class:"flow-root"},vs={class:"-mb-8"},fs={key:0,class:"absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"},hs={class:"relative flex space-x-3"},bs={class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},ys=["d"],ws={class:"min-w-0 flex-1 pt-1.5 flex justify-between space-x-4"},ps={class:"text-sm text-gray-900"},_s={class:"text-sm text-gray-500"},Ss={class:"text-right text-sm whitespace-nowrap text-gray-500"},Cs={class:"bg-white shadow rounded-lg p-6"},ks={class:"space-y-4"},js={class:"text-sm font-medium text-gray-900"},As={class:"text-xs text-gray-500"},Ms={class:"text-right"},Ts={class:"text-sm font-medium text-green-600"},Rs={__name:"Dashboard",props:{recentOrders:Array,orderStats:Object,countryRevenue:Object,leadStats:Object,quotationStats:Object,monthlyTrends:Array,topAgents:Array,recentActivities:Array,permissions:Object,taskStats:Object},setup(e){C({});const d=e;k(async()=>{localStorage.setItem("permissions",JSON.stringify(d.permissions))});const i=(n,s="United Kingdom")=>{const o={"United Kingdom":{locale:"en-GB",currency:"GBP"},"United States":{locale:"en-US",currency:"USD"},Canada:{locale:"en-CA",currency:"CAD"},Australia:{locale:"en-AU",currency:"AUD"}},c=Object.keys(o).find(_=>s==null?void 0:s.toLowerCase().includes(_.toLowerCase())),{locale:w,currency:g}=o[c]||o["United Kingdom"],p=new Intl.NumberFormat(w,{style:"currency",currency:g,currencyDisplay:"symbol"}).format(n);return`${g} ${p}`},h=n=>new Date(n).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),b=n=>{const s={lead:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z",quotation:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",order:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"};return s[n]||s.lead},y=n=>{const s={lead:"text-blue-600 bg-blue-100",quotation:"text-green-600 bg-green-100",order:"text-purple-600 bg-purple-100"};return s[n]||s.lead},u=j(()=>{const n=d.leadStats.total>0?(d.quotationStats.total/d.leadStats.total*100).toFixed(1):0,s=d.quotationStats.total>0?(d.orderStats.total/d.quotationStats.total*100).toFixed(1):0,o=d.leadStats.total>0?(d.orderStats.total/d.leadStats.total*100).toFixed(1):0;return{leadToQuotation:n,quotationToOrder:s,leadToOrder:o}});return(n,s)=>(r(),a(m,null,[v(A(T),{title:"Dashboard"}),v(S,null,{default:M(()=>[t("div",U,[s[42]||(s[42]=t("div",{class:"flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0 mb-8"},[t("div",null,[t("h1",{class:"text-3xl font-bold text-gray-900"},"Dashboard"),t("p",{class:"text-gray-600 mt-1"},"Complete overview of your business performance")])],-1)),t("div",R,[t("div",q,[t("div",D,[t("div",V,[s[1]||(s[1]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])])],-1)),t("div",B,[t("dl",null,[s[0]||(s[0]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Leads",-1)),t("dd",L,l(e.leadStats.total),1)])])])])]),t("div",H,[t("div",K,[t("div",F,[s[3]||(s[3]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM8 15a1 1 0 100-2 1 1 0 000 2zm4 0a1 1 0 100-2 1 1 0 000 2z","clip-rule":"evenodd"})])])],-1)),t("div",P,[t("dl",null,[s[2]||(s[2]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Orders",-1)),t("dd",Q,l(e.orderStats.total),1)])])])])]),t("div",$,[t("div",I,[t("div",G,[s[5]||(s[5]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),t("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z","clip-rule":"evenodd"})])])],-1)),t("div",N,[t("dl",null,[s[4]||(s[4]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Quotations",-1)),t("dd",E,l(e.quotationStats.total),1)])])])])]),t("div",J,[t("div",W,[t("div",Z,[s[7]||(s[7]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[t("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})])])],-1)),t("div",X,[t("dl",null,[s[6]||(s[6]=t("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Total Task",-1)),t("dd",Y,l(e.taskStats.total),1)])])])])])]),t("div",tt,[s[26]||(s[26]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-6"},"Revenue by Country",-1)),t("div",st,[s[16]||(s[16]=t("h4",{class:"text-md font-medium text-gray-900 mb-4"},"Total Sales Revenue",-1)),t("div",et,[t("div",lt,[t("div",ot,[s[9]||(s[9]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",dt,[s[8]||(s[8]=t("p",{class:"text-sm font-medium text-blue-600"},"UK Sales",-1)),t("p",nt,l(i(e.countryRevenue.total.uk,"United Kingdom")),1)])])]),t("div",it,[t("div",at,[s[11]||(s[11]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",rt,[s[10]||(s[10]=t("p",{class:"text-sm font-medium text-green-600"},"US Sales",-1)),t("p",ct,l(i(e.countryRevenue.total.us,"United States")),1)])])]),t("div",ut,[t("div",mt,[s[13]||(s[13]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",xt,[s[12]||(s[12]=t("p",{class:"text-sm font-medium text-purple-600"},"Canada Sales",-1)),t("p",gt,l(i(e.countryRevenue.total.canada,"Canada")),1)])])]),t("div",vt,[t("div",ft,[s[15]||(s[15]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",ht,[s[14]||(s[14]=t("p",{class:"text-sm font-medium text-yellow-600"},"Australia Sales",-1)),t("p",bt,l(i(e.countryRevenue.total.australia,"Australia")),1)])])])])]),t("div",null,[s[25]||(s[25]=t("h4",{class:"text-md font-medium text-gray-900 mb-4"},"This Month Sales Revenue",-1)),t("div",yt,[t("div",wt,[t("div",pt,[s[18]||(s[18]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",_t,[s[17]||(s[17]=t("p",{class:"text-sm font-medium text-blue-600"},"UK This Month",-1)),t("p",St,l(i(e.countryRevenue.monthly.uk,"United Kingdom")),1)])])]),t("div",Ct,[t("div",kt,[s[20]||(s[20]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",jt,[s[19]||(s[19]=t("p",{class:"text-sm font-medium text-green-600"},"US This Month",-1)),t("p",At,l(i(e.countryRevenue.monthly.us,"United States")),1)])])]),t("div",Mt,[t("div",Tt,[s[22]||(s[22]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",Ot,[s[21]||(s[21]=t("p",{class:"text-sm font-medium text-purple-600"},"Canada This Month",-1)),t("p",zt,l(i(e.countryRevenue.monthly.canada,"Canada")),1)])])]),t("div",Ut,[t("div",Rt,[s[24]||(s[24]=t("div",{class:"flex-shrink-0"},[t("span",{class:"text-2xl"})],-1)),t("div",qt,[s[23]||(s[23]=t("p",{class:"text-sm font-medium text-yellow-600"},"Australia This Month",-1)),t("p",Dt,l(i(e.countryRevenue.monthly.australia,"Australia")),1)])])])])])]),t("div",Vt,[s[30]||(s[30]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Conversion Rates",-1)),t("div",Bt,[t("div",Lt,[t("div",Ht,l(u.value.leadToQuotation)+"%",1),s[27]||(s[27]=t("div",{class:"text-sm text-gray-500"},"Lead \u2192 Quotation",-1))]),t("div",Kt,[t("div",Ft,l(u.value.quotationToOrder)+"%",1),s[28]||(s[28]=t("div",{class:"text-sm text-gray-500"},"Quotation \u2192 Order",-1))]),t("div",Pt,[t("div",Qt,l(u.value.leadToOrder)+"%",1),s[29]||(s[29]=t("div",{class:"text-sm text-gray-500"},"Lead \u2192 Order",-1))])])]),t("div",$t,[t("div",It,[s[34]||(s[34]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Sales Pipeline",-1)),t("div",Gt,[t("div",null,[t("div",Nt,[s[31]||(s[31]=t("span",null,"Leads",-1)),t("span",null,l(e.leadStats.total),1)]),t("div",Et,[t("div",{class:"bg-blue-600 h-2 rounded-full",style:x(`width: ${e.leadStats.total>0?100:0}%`)},null,4)]),t("div",Jt,[t("span",null,"New: "+l(e.leadStats.new),1),t("span",null,"Won: "+l(e.leadStats.won),1)])]),t("div",null,[t("div",Wt,[s[32]||(s[32]=t("span",null,"Quotations",-1)),t("span",null,l(e.quotationStats.total),1)]),t("div",Zt,[t("div",{class:"bg-green-600 h-2 rounded-full",style:x(`width: ${e.quotationStats.total>0?e.quotationStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",Xt,[t("span",null,"Pending: "+l(e.quotationStats.pending),1),t("span",null,"Order Placed: "+l(e.quotationStats.order_placed),1)])]),t("div",null,[t("div",Yt,[s[33]||(s[33]=t("span",null,"Orders",-1)),t("span",null,l(e.orderStats.total),1)]),t("div",ts,[t("div",{class:"bg-purple-600 h-2 rounded-full",style:x(`width: ${e.orderStats.total>0?e.orderStats.total/Math.max(e.leadStats.total,1)*100:0}%`)},null,4)]),t("div",ss,[t("span",null,"Confirmed: "+l(e.orderStats.confirmed),1),t("span",null,"Delivered: "+l(e.orderStats.delivered),1)])])])]),t("div",es,[s[39]||(s[39]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Order Status Distribution",-1)),t("div",ls,[t("div",os,[s[35]||(s[35]=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-blue-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Confirmed")],-1)),t("span",ds,l(e.orderStats.confirmed),1)]),t("div",ns,[s[36]||(s[36]=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-purple-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Under Production")],-1)),t("span",is,l(e.orderStats.under_production),1)]),t("div",as,[s[37]||(s[37]=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-indigo-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Shipped")],-1)),t("span",rs,l(e.orderStats.shipped),1)]),t("div",cs,[s[38]||(s[38]=t("div",{class:"flex items-center"},[t("div",{class:"w-3 h-3 bg-green-400 rounded-full mr-3"}),t("span",{class:"text-sm text-gray-700"},"Delivered")],-1)),t("span",us,l(e.orderStats.delivered),1)])])])]),t("div",ms,[t("div",xs,[s[40]||(s[40]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Recent Activities",-1)),t("div",gs,[t("ul",vs,[(r(!0),a(m,null,f(e.recentActivities,(o,c)=>(r(),a("li",{key:c,class:"relative pb-8"},[c!==e.recentActivities.length-1?(r(),a("div",fs)):O("",!0),t("div",hs,[t("div",null,[t("span",{class:z(["h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white",y(o.type)])},[(r(),a("svg",bs,[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:b(o.type)},null,8,ys)]))],2)]),t("div",ws,[t("div",null,[t("p",ps,l(o.title),1),t("p",_s,l(o.description),1)]),t("div",Ss,[t("time",null,l(h(o.created_at)),1)])])])]))),128))])])]),t("div",Cs,[s[41]||(s[41]=t("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"Top Performing Agents",-1)),t("div",ks,[(r(!0),a(m,null,f(e.topAgents,o=>(r(),a("div",{key:o.id,class:"flex items-center justify-between"},[t("div",null,[t("p",js,l(o.first_name)+" "+l(o.last_name),1),t("p",As,l(o.orders_count)+" orders",1)]),t("div",Ms,[t("p",Ts,l(i(o.orders_sum_total_amount)),1)])]))),128))])])])])]),_:1})],64))}};export{Rs as default};
