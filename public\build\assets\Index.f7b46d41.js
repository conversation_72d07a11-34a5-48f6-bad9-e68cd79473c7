import{j as w,a as d,b as r,u as x,w as u,F as T,d as a,Z as q,e,q as b,y as m,f as c,t as i,r as G,p as M,N as j,z as Y,g as y}from"./app.0e820f21.js";import{_ as Z}from"./AdminLayout.687face1.js";import{_ as h}from"./SearchableDropdownNew.0cffdca8.js";import{_ as k}from"./InputLabel.c491b164.js";import{_ as B}from"./CreateButton.f13c50e2.js";import{s as J,_ as K}from"./sortAndSearch.29e714e8.js";import{M as Q}from"./Modal.c671de5e.js";import{_ as R}from"./SecondaryButton.f2b207b7.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                          */const W={class:"animate-top"},X={class:"bg-white p-4 shadow sm:p-6 rounded-lg border"},ee={class:"flex justify-between items-center mb-6"},te={class:"flex space-x-2"},se={key:0,class:"sm:flex-none"},oe={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},le={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},re={class:"flex items-center"},ne={class:"ml-4"},ie={class:"text-2xl font-semibold text-blue-900"},ae={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},de={class:"flex items-center"},ue={class:"ml-4"},me={class:"text-2xl font-semibold text-yellow-900"},ce={class:"bg-red-50 border border-red-200 rounded-lg p-4"},ge={class:"flex items-center"},xe={class:"ml-4"},pe={class:"text-2xl font-semibold text-red-900"},fe={class:"bg-green-50 border border-green-200 rounded-lg p-4"},ve={class:"flex items-center"},be={class:"ml-4"},ye={class:"text-2xl font-semibold text-green-900"},he={class:"bg-gray-50 p-4 rounded-lg mb-6"},ke={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},_e={class:"relative mt-2"},we={class:"relative mt-2"},Ce={class:"relative mt-2"},Ve={class:"relative mt-2"},Te={key:0,class:"text-center py-12"},Me={class:"mt-6"},je={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6"},Be={class:"flex items-start justify-between"},Se={class:"flex-1"},$e={class:"text-sm font-semibold text-gray-900 mb-1"},De={key:0,class:"text-xs text-gray-500"},Oe={class:"flex flex-col items-end space-y-2"},Ae={class:"space-y-2 mb-4"},ze={class:"flex items-center text-sm text-gray-700"},Ne={key:0,class:"ml-1 text-red-500"},Ue={class:"flex items-center text-xs text-gray-700"},Ee={class:"flex space-x-2"},He=["onClick"],Fe=["onClick"],Pe={class:"p-6"},Ie={class:"mt-6 flex justify-end space-x-4"},Le={class:"w-44"},et={__name:"Index",props:{tasks:Object,users:Array,stats:Object,status:Array,types:Array,priorities:Array,permissions:Object},setup(l){const{form:S}=J("tasks.index"),n=w({status:"",type:"",priority:"",assigned_to:""}),$=o=>{n.value.status=o,p()},D=o=>{n.value.type=o,p()},O=o=>{n.value.priority=o,p()},A=o=>{n.value.assigned_to=o,p()},p=()=>{j.get(route("tasks.index"),n.value,{preserveState:!0,preserveScroll:!0})},f=w(!1),C=w(null),z=o=>{C.value=o,f.value=!0},V=()=>{f.value=!1},N=()=>{const o=C.value,t=route("tasks.complete",o);Y.post(t).then(s=>{console.log("Task completed successfully",s.data.message),f.value=!1,j.reload()}).catch(s=>{var g,v;console.error("Error completing task:",((v=(g=s.response)==null?void 0:g.data)==null?void 0:v.error)||s.message)})},U=o=>{const t=new Date(o),s=String(t.getDate()).padStart(2,"0"),g=String(t.getMonth()+1).padStart(2,"0"),v=t.getFullYear();return`${s}/${g}/${v}`},E=o=>o.replace("_"," ").replace(/\b\w/g,t=>t.toUpperCase()),H=o=>o.replace("_"," ").replace(/\b\w/g,t=>t.toUpperCase()),_=o=>o.status!=="completed"&&new Date(o.due_date)<new Date,F=o=>({call:"bg-blue-100 text-blue-800",follow_up:"bg-yellow-100 text-yellow-800",meeting:"bg-purple-100 text-purple-800",email:"bg-green-100 text-green-800",quote_follow_up:"bg-orange-100 text-orange-800",order_follow_up:"bg-red-100 text-red-800",general:"bg-gray-100 text-gray-800",reminder:"bg-pink-100 text-pink-800"})[o]||"bg-gray-100 text-gray-800",P=o=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",I=o=>({pending:"bg-yellow-100 text-yellow-800",in_progress:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[o]||"bg-gray-100 text-gray-800",L=o=>{S.delete(route("tasks.destroy",{task:o}))};return(o,t)=>(a(),d(T,null,[r(x(q),{title:"Tasks"}),r(Z,null,{default:u(()=>[e("div",W,[e("div",X,[e("div",ee,[t[6]||(t[6]=e("div",null,[e("h2",{class:"text-2xl font-semibold leading-7 text-gray-900"},"Tasks"),e("p",{class:"text-sm text-gray-600 mt-1"},"Manage your tasks and follow-ups")],-1)),e("div",te,[r(x(b),{href:o.route("tasks.dashboard"),class:"inline-flex items-center px-4 py-2 bg-indigo-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-indigo-700"},{default:u(()=>t[4]||(t[4]=[m(" \u{1F4CA} Dashboard ")])),_:1,__:[4]},8,["href"]),l.permissions.canCreateTask?(a(),d("div",se,[r(B,{href:o.route("tasks.create")},{default:u(()=>t[5]||(t[5]=[m(" Add Task ")])),_:1,__:[5]},8,["href"])])):c("",!0)])]),e("div",oe,[e("div",le,[e("div",re,[t[8]||(t[8]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})])],-1)),e("div",ne,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-blue-600"},"Total Tasks",-1)),e("p",ie,i(l.stats.total),1)])])]),e("div",ae,[e("div",de,[t[10]||(t[10]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ue,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-yellow-600"},"Pending",-1)),e("p",me,i(l.stats.pending),1)])])]),e("div",ce,[e("div",ge,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",xe,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-red-600"},"Overdue",-1)),e("p",pe,i(l.stats.overdue),1)])])]),e("div",fe,[e("div",ve,[t[14]||(t[14]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",be,[t[13]||(t[13]=e("p",{class:"text-sm font-medium text-green-600"},"Due Today",-1)),e("p",ye,i(l.stats.due_today),1)])])])]),e("div",he,[e("div",ke,[e("div",null,[r(k,{for:"agent_filter",value:"Status"}),e("div",_e,[r(h,{options:l.status,modelValue:n.value.status,"onUpdate:modelValue":t[0]||(t[0]=s=>n.value.status=s),onOnchange:$},null,8,["options","modelValue"])])]),e("div",null,[r(k,{for:"agent_filter",value:"Type"}),e("div",we,[r(h,{options:l.types,modelValue:n.value.type,"onUpdate:modelValue":t[1]||(t[1]=s=>n.value.type=s),onOnchange:D},null,8,["options","modelValue"])])]),e("div",null,[r(k,{for:"agent_filter",value:"Priority"}),e("div",Ce,[r(h,{options:l.priorities,modelValue:n.value.priority,"onUpdate:modelValue":t[2]||(t[2]=s=>n.value.priority=s),onOnchange:O},null,8,["options","modelValue"])])]),e("div",null,[r(k,{for:"agent_filter",value:"Assigned To"}),e("div",Ve,[r(h,{options:l.users,modelValue:n.value.assigned_to,"onUpdate:modelValue":t[3]||(t[3]=s=>n.value.assigned_to=s),onOnchange:A},null,8,["options","modelValue"])])])])]),l.tasks.data.length===0?(a(),d("div",Te,[t[16]||(t[16]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)),t[17]||(t[17]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900"},"No tasks found",-1)),t[18]||(t[18]=e("p",{class:"mt-1 text-sm text-gray-500"},"Get started by creating a new task.",-1)),e("div",Me,[r(x(b),{href:o.route("tasks.create"),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"},{default:u(()=>t[15]||(t[15]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),m(" New Task ")])),_:1,__:[15]},8,["href"])])])):c("",!0)]),e("div",je,[(a(!0),d(T,null,G(l.tasks.data,s=>(a(),d("div",{key:s.id,class:"bg-white border rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Be,[e("div",Se,[e("h3",$e,i(s.title),1),s.lead?(a(),d("p",De," \u{1F4CB} "+i(s.lead.client_name)+" ("+i(s.lead.company_name)+") ",1)):c("",!0)]),e("div",Oe,[e("span",{class:y(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",P(s.priority)])},i(s.priority),3),e("span",{class:y(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",I(s.status)])},i(H(s.status)),3)])]),e("div",Ae,[e("div",ze,[t[19]||(t[19]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})],-1)),m(" "+i(s.assigned_to.first_name)+" "+i(s.assigned_to.last_name),1)]),e("div",{class:y(["flex items-center text-xs",{"text-red-600 font-semibold":_(s),"text-gray-600":!_(s)}])},[t[20]||(t[20]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),m(" "+i(U(s.due_date))+" ",1),_(s)?(a(),d("span",Ne,"\u26A0\uFE0F Overdue")):c("",!0)],2),e("div",Ue,[e("span",{class:y(["inline-flex px-2 py-1 text-xs font-semibold rounded-full",F(s.type)])},i(E(s.type)),3)])]),e("div",Ee,[r(x(b),{href:o.route("tasks.show",s.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-indigo-600 bg-indigo-50 rounded-md hover:bg-indigo-100"},{default:u(()=>t[21]||(t[21]=[m(" View ")])),_:2,__:[21]},1032,["href"]),l.permissions.canEditTask?(a(),M(x(b),{key:0,href:o.route("tasks.edit",s.id),class:"flex-1 text-center px-3 py-2 text-xs font-semibold text-green-600 bg-green-50 rounded-md hover:bg-green-100"},{default:u(()=>t[22]||(t[22]=[m(" Edit ")])),_:2,__:[22]},1032,["href"])):c("",!0),s.status!=="completed"&&l.permissions.canEditTask?(a(),d("button",{key:1,onClick:g=>z(s.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"}," Complete ",8,He)):c("",!0),l.permissions.canDeleteTask?(a(),d("button",{key:2,onClick:g=>L(s.id),class:"flex-1 px-3 py-2 text-xs font-semibold text-red-600 bg-red-50 rounded-md hover:bg-red-100"}," Delete ",8,Fe)):c("",!0)])]))),128))]),l.tasks.links&&l.tasks.links.length>0?(a(),M(K,{key:0,class:"mt-6",links:l.tasks.links},null,8,["links"])):c("",!0)]),r(Q,{show:f.value,onClose:V},{default:u(()=>[e("div",Pe,[t[25]||(t[25]=e("h2",{class:"text-lg font-medium text-gray-900"}," Are you sure you want to complete this task? ",-1)),e("div",Ie,[r(R,{onClick:V},{default:u(()=>t[23]||(t[23]=[m("Cancel")])),_:1,__:[23]}),e("div",Le,[r(B,{class:"w-44",onClick:N},{default:u(()=>t[24]||(t[24]=[m(" Complete Task ")])),_:1,__:[24]})])])])]),_:1},8,["show"])]),_:1})],64))}};export{et as default};
